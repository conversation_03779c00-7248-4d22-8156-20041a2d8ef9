"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DollarSign,
  TrendingUp,
  Target,
  Calendar,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  PieChart,
  Plus,
  Briefcase,
  ChevronDown,
  ChevronRight,
  Settings,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { PercentageInputField } from "@/components/calculator/core/InputField";
import { RecordForm } from "./portfolio-forms-v2";

// 类型定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface GlobalHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  currency: string;
  color: string;
  records: InvestmentRecord[];
  totalInvested: number;
  currentValue: number;
  totalShares: number;
  averageCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
}

interface PortfolioHolding {
  holdingId: string;
  targetAllocation: number;
  isVisible: boolean;
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: string;
  holdings: PortfolioHolding[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Overview View - 基于真实数据的图表
export function OverviewView({
  portfolio,
  holdingsWithData,
  chartData,
  onEditPortfolio,
}: {
  portfolio: Portfolio;
  holdingsWithData: any[];
  chartData: any[];
  onEditPortfolio: (portfolio: Portfolio) => void;
}) {
  // 计算总体统计（基于真实数据）
  const totalValue = holdingsWithData.reduce(
    (sum, holding) => sum + (holding.currentValue || 0),
    0,
  );
  const totalInvested = holdingsWithData.reduce(
    (sum, holding) => sum + (holding.totalInvested || 0),
    0,
  );
  const totalGain = totalValue - totalInvested;
  const totalGainPercent =
    totalInvested > 0 ? (totalGain / totalInvested) * 100 : 0;

  // 准备饼图数据
  const pieData = holdingsWithData.map((holding) => ({
    name: holding.symbol || holding.name,
    value: holding.currentValue || 0,
    color: holding.color,
    allocation: holding.targetAllocation,
    actualAllocation:
      totalValue > 0 ? ((holding.currentValue || 0) / totalValue) * 100 : 0,
  }));

  return (
    <div className="space-y-8">
      {/* Portfolio Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                {portfolio.name}
              </CardTitle>
              <p className="text-muted-foreground mt-1">
                {portfolio.description}
              </p>
              <div className="mt-2 flex items-center gap-4">
                <Badge
                  variant={
                    portfolio.riskLevel === "conservative"
                      ? "secondary"
                      : portfolio.riskLevel === "moderate"
                        ? "default"
                        : "destructive"
                  }
                >
                  {portfolio.riskLevel}
                </Badge>
                <span className="text-muted-foreground text-sm">
                  {portfolio.strategy}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditPortfolio(portfolio)}
            >
              <Edit2 className="mr-2 h-4 w-4" />
              Edit Portfolio
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-4">
            <div className="text-center">
              <DollarSign className="mx-auto mb-2 h-8 w-8 text-blue-500" />
              <p className="text-muted-foreground text-sm">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(totalValue, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <TrendingUp className="mx-auto mb-2 h-8 w-8 text-green-500" />
              <p className="text-muted-foreground text-sm">Total Gain</p>
              <p
                className={`text-2xl font-bold ${totalGain >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {formatCurrency(totalGain, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <BarChart3 className="mx-auto mb-2 h-8 w-8 text-purple-500" />
              <p className="text-muted-foreground text-sm">Return %</p>
              <p
                className={`text-2xl font-bold ${totalGainPercent >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {CurrencyUtils.formatPercentage(totalGainPercent)}
              </p>
            </div>
            <div className="text-center">
              <Target className="mx-auto mb-2 h-8 w-8 text-orange-500" />
              <p className="text-muted-foreground text-sm">Holdings</p>
              <p className="text-2xl font-bold">{holdingsWithData.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Real Data Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Portfolio Performance (Real Data)
            </CardTitle>
          </CardHeader>
          <CardContent>
            {chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip
                    formatter={(value: number) => [
                      formatCurrency(value, portfolio.currency),
                      "Value",
                    ]}
                  />
                  <Legend />
                  {holdingsWithData
                    .filter((h) => h.isVisible)
                    .map((holding) => (
                      <Area
                        key={holding.id}
                        type="monotone"
                        dataKey={holding.symbol || holding.name}
                        stackId="1"
                        stroke={holding.color}
                        fill={holding.color}
                        fillOpacity={0.6}
                      />
                    ))}
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-muted-foreground flex h-[300px] items-center justify-center">
                <div className="text-center">
                  <Calendar className="mx-auto mb-4 h-12 w-12" />
                  <p>No investment records yet</p>
                  <p className="text-sm">
                    Add investment records to see real performance data
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Allocation Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Asset Allocation
            </CardTitle>
          </CardHeader>
          <CardContent>
            {totalValue > 0 ? (
              <>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={pieData.filter(
                        (_, index) => holdingsWithData[index].isVisible,
                      )}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ actualAllocation }) =>
                        `${actualAllocation.toFixed(1)}%`
                      }
                    >
                      {pieData
                        .filter((_, index) => holdingsWithData[index].isVisible)
                        .map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number) => [
                        formatCurrency(value, portfolio.currency),
                        "Value",
                      ]}
                    />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>

                {/* Allocation vs Target */}
                <div className="mt-4 space-y-2">
                  {holdingsWithData
                    .filter((h) => h.isVisible)
                    .map((holding) => {
                      const actualAllocation =
                        totalValue > 0
                          ? ((holding.currentValue || 0) / totalValue) * 100
                          : 0;
                      const difference =
                        actualAllocation - holding.targetAllocation;

                      return (
                        <div
                          key={holding.id}
                          className="flex items-center justify-between text-sm"
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className="h-3 w-3 rounded-full"
                              style={{ backgroundColor: holding.color }}
                            />
                            <span>{holding.symbol || holding.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span>{actualAllocation.toFixed(1)}%</span>
                            <span className="text-muted-foreground">vs</span>
                            <span>{holding.targetAllocation}%</span>
                            <span
                              className={`font-medium ${
                                Math.abs(difference) < 1
                                  ? "text-green-600"
                                  : Math.abs(difference) < 5
                                    ? "text-yellow-600"
                                    : "text-red-600"
                              }`}
                            >
                              ({difference > 0 ? "+" : ""}
                              {difference.toFixed(1)}%)
                            </span>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </>
            ) : (
              <div className="text-muted-foreground flex h-[300px] items-center justify-center">
                <div className="text-center">
                  <PieChart className="mx-auto mb-4 h-12 w-12" />
                  <p>No investment data</p>
                  <p className="text-sm">
                    Add investment records to see allocation
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Holdings Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Holdings Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Holding</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Total Invested</TableHead>
                <TableHead>Gain/Loss</TableHead>
                <TableHead>Target %</TableHead>
                <TableHead>Actual %</TableHead>
                <TableHead>Records</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {holdingsWithData.map((holding) => {
                const actualAllocation =
                  totalValue > 0
                    ? ((holding.currentValue || 0) / totalValue) * 100
                    : 0;

                return (
                  <TableRow key={holding.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: holding.color }}
                        />
                        <div>
                          <p className="font-medium">{holding.name}</p>
                          {holding.symbol && (
                            <p className="text-muted-foreground text-sm">
                              {holding.symbol}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {holding.type?.replace("_", " ").toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(
                        holding.currentValue || 0,
                        holding.currency,
                      )}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(
                        holding.totalInvested || 0,
                        holding.currency,
                      )}
                    </TableCell>
                    <TableCell>
                      <div
                        className={
                          (holding.unrealizedGain || 0) >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {formatCurrency(
                          holding.unrealizedGain || 0,
                          holding.currency,
                        )}
                        <br />
                        <span className="text-xs">
                          (
                          {CurrencyUtils.formatPercentage(
                            holding.unrealizedGainPercent || 0,
                          )}
                          )
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{holding.targetAllocation}%</TableCell>
                    <TableCell>{actualAllocation.toFixed(1)}%</TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {holding.records?.length || 0} records
                      </Badge>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

// Holdings Pool View - 管理全局投资标的
export function HoldingsPoolView({
  globalHoldings,
  onEditHolding,
  onDeleteHolding,
  onAddRecord,
  onUpdateRecord,
  onDeleteRecord,
}: {
  globalHoldings: GlobalHolding[];
  onEditHolding: (holding: GlobalHolding) => void;
  onDeleteHolding: (holdingId: string) => void;
  onAddRecord?: (holdingId: string, record: Partial<InvestmentRecord>) => void;
  onUpdateRecord?: (
    holdingId: string,
    recordId: string,
    record: Partial<InvestmentRecord>,
  ) => void;
  onDeleteRecord?: (holdingId: string, recordId: string) => void;
}) {
  const [expandedHoldings, setExpandedHoldings] = useState<Set<string>>(
    new Set(),
  );
  const [editingRecord, setEditingRecord] = useState<{
    holdingId: string;
    record: InvestmentRecord;
  } | null>(null);
  const [addingRecordToHolding, setAddingRecordToHolding] = useState<
    string | null
  >(null);

  const toggleHoldingExpansion = (holdingId: string) => {
    const newExpanded = new Set(expandedHoldings);
    if (newExpanded.has(holdingId)) {
      newExpanded.delete(holdingId);
    } else {
      newExpanded.add(holdingId);
    }
    setExpandedHoldings(newExpanded);
  };

  const handleAddRecord = (
    holdingId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    if (onAddRecord) {
      onAddRecord(holdingId, recordData);
    }
    setAddingRecordToHolding(null);
  };

  const handleUpdateRecord = (recordData: Partial<InvestmentRecord>) => {
    if (editingRecord && onUpdateRecord) {
      onUpdateRecord(
        editingRecord.holdingId,
        editingRecord.record.id,
        recordData,
      );
      setEditingRecord(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Holdings Pool Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Global Holdings Pool
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Holdings</p>
              <p className="text-2xl font-bold">{globalHoldings.length}</p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Records</p>
              <p className="text-2xl font-bold">
                {globalHoldings.reduce((sum, h) => sum + h.records.length, 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Invested</p>
              <p className="text-2xl font-bold">
                {formatCurrency(
                  globalHoldings.reduce((sum, h) => sum + h.totalInvested, 0),
                  globalHoldings[0]?.currency || "USD",
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Holdings List with Expandable Records */}
      <div className="space-y-4">
        {globalHoldings.map((holding) => {
          const isExpanded = expandedHoldings.has(holding.id);

          return (
            <Card key={holding.id} className="overflow-hidden">
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <CardHeader
                    className="hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => toggleHoldingExpansion(holding.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <div
                            className="h-4 w-4 rounded-full"
                            style={{ backgroundColor: holding.color }}
                          />
                        </div>

                        <div>
                          <CardTitle className="text-lg">
                            {holding.name}
                          </CardTitle>
                          <div className="mt-1 flex items-center gap-4">
                            {holding.symbol && (
                              <Badge variant="outline">{holding.symbol}</Badge>
                            )}
                            <Badge variant="secondary">
                              {holding.type.replace("_", " ").toUpperCase()}
                            </Badge>
                            <span className="text-muted-foreground text-sm">
                              {holding.records.length} records
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        {/* Key Metrics */}
                        <div className="text-right">
                          <p className="font-semibold">
                            {formatCurrency(
                              holding.currentValue,
                              holding.currency,
                            )}
                          </p>
                          <p
                            className={`text-sm ${
                              holding.unrealizedGain >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              holding.unrealizedGain,
                              holding.currency,
                            )}
                            (
                            {CurrencyUtils.formatPercentage(
                              holding.unrealizedGainPercent,
                            )}
                            )
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div
                          className="flex items-center gap-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setAddingRecordToHolding(holding.id)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditHolding(holding)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteHolding(holding.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    {/* Holding Details */}
                    <div className="bg-muted/30 mb-6 grid grid-cols-2 gap-4 rounded-lg p-4 md:grid-cols-4">
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Invested
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.totalInvested,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Shares
                        </p>
                        <p className="font-semibold">
                          {holding.totalShares.toFixed(3)}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Average Cost
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.averageCost,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Current Value
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.currentValue,
                            holding.currency,
                          )}
                        </p>
                      </div>
                    </div>

                    {/* Investment Records */}
                    <div>
                      <div className="mb-4 flex items-center justify-between">
                        <h4 className="font-medium">Investment Records</h4>
                        <Button
                          size="sm"
                          onClick={() => setAddingRecordToHolding(holding.id)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Record
                        </Button>
                      </div>

                      {holding.records.length === 0 ? (
                        <div className="bg-muted/20 rounded-lg py-8 text-center">
                          <Calendar className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                          <p className="text-muted-foreground">
                            No investment records yet
                          </p>
                          <p className="text-muted-foreground text-sm">
                            Add your first investment record
                          </p>
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Price</TableHead>
                              <TableHead>Shares</TableHead>
                              <TableHead>Fees</TableHead>
                              <TableHead>Note</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {holding.records.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell>
                                  {new Date(record.date).toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={
                                      record.type === "buy"
                                        ? "default"
                                        : record.type === "sell"
                                          ? "destructive"
                                          : "secondary"
                                    }
                                  >
                                    {record.type.toUpperCase()}
                                  </Badge>
                                </TableCell>
                                <TableCell className="font-medium">
                                  {formatCurrency(
                                    record.amount,
                                    holding.currency,
                                  )}
                                </TableCell>
                                <TableCell>
                                  {record.price
                                    ? formatCurrency(
                                        record.price,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.shares
                                    ? record.shares.toFixed(4)
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.fees
                                    ? formatCurrency(
                                        record.fees,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell className="max-w-32 truncate">
                                  {record.note || "-"}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        setEditingRecord({
                                          holdingId: holding.id,
                                          record,
                                        })
                                      }
                                    >
                                      <Edit2 className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        onDeleteRecord &&
                                        onDeleteRecord(holding.id, record.id)
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      {/* Add Record Dialog */}
      {addingRecordToHolding && (
        <Dialog
          open={!!addingRecordToHolding}
          onOpenChange={() => setAddingRecordToHolding(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Investment Record</DialogTitle>
              <DialogDescription>
                Add a new investment record to{" "}
                {
                  globalHoldings.find((h) => h.id === addingRecordToHolding)
                    ?.name
                }
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              onSave={handleAddRecord.bind(null, addingRecordToHolding)}
              currency={
                globalHoldings.find((h) => h.id === addingRecordToHolding)
                  ?.currency || "USD"
              }
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Record Dialog */}
      {editingRecord && (
        <Dialog
          open={!!editingRecord}
          onOpenChange={() => setEditingRecord(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Investment Record</DialogTitle>
              <DialogDescription>
                Edit investment record for{" "}
                {
                  globalHoldings.find((h) => h.id === editingRecord.holdingId)
                    ?.name
                }
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              initialData={editingRecord.record}
              onSave={handleUpdateRecord}
              currency={
                globalHoldings.find((h) => h.id === editingRecord.holdingId)
                  ?.currency || "USD"
              }
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Manage Portfolio View - 管理组合中的Holdings
export function ManagePortfolioView({
  portfolio,
  globalHoldings,
  onUpdatePortfolio,
}: {
  portfolio: Portfolio;
  globalHoldings: GlobalHolding[];
  onUpdatePortfolio: (updates: Partial<Portfolio>) => void;
}) {
  const [selectedHoldings, setSelectedHoldings] = useState<PortfolioHolding[]>(
    portfolio.holdings,
  );
  const [hasChanges, setHasChanges] = useState(false);

  // 检查holding是否在组合中
  const isHoldingInPortfolio = (holdingId: string) => {
    return selectedHoldings.some((h) => h.holdingId === holdingId);
  };

  // 获取holding在组合中的配置
  const getHoldingConfig = (holdingId: string) => {
    return selectedHoldings.find((h) => h.holdingId === holdingId);
  };

  // 添加或移除holding
  const toggleHolding = (holdingId: string) => {
    if (isHoldingInPortfolio(holdingId)) {
      // 移除
      setSelectedHoldings((prev) =>
        prev.filter((h) => h.holdingId !== holdingId),
      );
    } else {
      // 添加
      setSelectedHoldings((prev) => [
        ...prev,
        {
          holdingId,
          targetAllocation: 0,
          isVisible: true,
        },
      ]);
    }
    setHasChanges(true);
  };

  // 更新配置比例
  const updateAllocation = (holdingId: string, allocation: number) => {
    setSelectedHoldings((prev) =>
      prev.map((h) =>
        h.holdingId === holdingId ? { ...h, targetAllocation: allocation } : h,
      ),
    );
    setHasChanges(true);
  };

  // 更新可见性
  const updateVisibility = (holdingId: string, isVisible: boolean) => {
    setSelectedHoldings((prev) =>
      prev.map((h) => (h.holdingId === holdingId ? { ...h, isVisible } : h)),
    );
    setHasChanges(true);
  };

  // 保存更改
  const saveChanges = () => {
    onUpdatePortfolio({ holdings: selectedHoldings });
    setHasChanges(false);
  };

  // 重置更改
  const resetChanges = () => {
    setSelectedHoldings(portfolio.holdings);
    setHasChanges(false);
  };

  // 计算总配置比例
  const totalAllocation = selectedHoldings.reduce(
    (sum, h) => sum + h.targetAllocation,
    0,
  );

  return (
    <div className="space-y-6">
      {/* Portfolio Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Manage Portfolio Holdings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <p className="text-muted-foreground text-sm">Portfolio</p>
              <p className="font-semibold">{portfolio.name}</p>
            </div>
            <div>
              <p className="text-muted-foreground text-sm">Selected Holdings</p>
              <p className="font-semibold">{selectedHoldings.length}</p>
            </div>
            <div>
              <p className="text-muted-foreground text-sm">Total Allocation</p>
              <p
                className={`font-semibold ${
                  Math.abs(totalAllocation - 100) < 0.1
                    ? "text-green-600"
                    : totalAllocation > 100
                      ? "text-red-600"
                      : "text-yellow-600"
                }`}
              >
                {totalAllocation.toFixed(1)}%
              </p>
            </div>
          </div>

          {hasChanges && (
            <div className="mt-4 flex items-center gap-2">
              <Button onClick={saveChanges}>Save Changes</Button>
              <Button variant="outline" onClick={resetChanges}>
                Reset
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Holdings Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Holdings for Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Select</TableHead>
                <TableHead>Holding</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Total Invested</TableHead>
                <TableHead>Gain/Loss</TableHead>
                <TableHead>Target %</TableHead>
                <TableHead>Visible</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {globalHoldings.map((holding) => {
                const isSelected = isHoldingInPortfolio(holding.id);
                const config = getHoldingConfig(holding.id);

                return (
                  <TableRow
                    key={holding.id}
                    className={isSelected ? "bg-muted/30" : ""}
                  >
                    <TableCell>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleHolding(holding.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: holding.color }}
                        />
                        <div>
                          <p className="font-medium">{holding.name}</p>
                          {holding.symbol && (
                            <p className="text-muted-foreground text-sm">
                              {holding.symbol}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {holding.type.replace("_", " ").toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(holding.currentValue, holding.currency)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(holding.totalInvested, holding.currency)}
                    </TableCell>
                    <TableCell>
                      <div
                        className={
                          holding.unrealizedGain >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {formatCurrency(
                          holding.unrealizedGain,
                          holding.currency,
                        )}
                        <br />
                        <span className="text-xs">
                          (
                          {CurrencyUtils.formatPercentage(
                            holding.unrealizedGainPercent,
                          )}
                          )
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isSelected ? (
                        <div className="w-24">
                          <PercentageInputField
                            value={config?.targetAllocation || 0}
                            onChange={(value) =>
                              updateAllocation(holding.id, value)
                            }
                            min={0}
                            max={100}
                            showSlider={false}
                          />
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {isSelected ? (
                        <Checkbox
                          checked={config?.isVisible || false}
                          onCheckedChange={(checked) =>
                            updateVisibility(holding.id, !!checked)
                          }
                        />
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Allocation Summary */}
      {selectedHoldings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Allocation Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedHoldings.map((holding) => {
                const globalHolding = globalHoldings.find(
                  (gh) => gh.id === holding.holdingId,
                );
                if (!globalHolding) return null;

                return (
                  <div
                    key={holding.holdingId}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: globalHolding.color }}
                      />
                      <span>{globalHolding.symbol || globalHolding.name}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="font-medium">
                        {holding.targetAllocation}%
                      </span>
                      <div className="h-2 w-32 rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-blue-600 transition-all duration-300"
                          style={{
                            width: `${Math.min(holding.targetAllocation, 100)}%`,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Total */}
              <div className="flex items-center justify-between border-t pt-3 font-semibold">
                <span>Total Allocation</span>
                <span
                  className={
                    Math.abs(totalAllocation - 100) < 0.1
                      ? "text-green-600"
                      : totalAllocation > 100
                        ? "text-red-600"
                        : "text-yellow-600"
                  }
                >
                  {totalAllocation.toFixed(1)}%
                </span>
              </div>

              {Math.abs(totalAllocation - 100) > 0.1 && (
                <div className="text-muted-foreground text-sm">
                  {totalAllocation > 100
                    ? "⚠️ Total allocation exceeds 100%. Please adjust."
                    : "⚠️ Total allocation is less than 100%. Consider adjusting."}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
