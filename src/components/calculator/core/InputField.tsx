"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>lider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import { SupportedCurrency, CurrencyInfo } from "@/lib/calculations/types";
import { getAllCurrencies } from "@/lib/utils/currency";
import CurrencyInput from "react-currency-input-field";

interface BaseInputFieldProps {
  label: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
}

interface NumberInputFieldProps extends BaseInputFieldProps {
  type: "number";
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  suffix?: string;
}

interface CurrencyInputFieldProps extends BaseInputFieldProps {
  type: "currency";
  value: number;
  onChange: (value: number) => void;
  currency: SupportedCurrency;
  onCurrencyChange?: (currency: SupportedCurrency) => void;
  showCurrencySelector?: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
}

interface PercentageInputFieldProps extends BaseInputFieldProps {
  type: "percentage";
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  showSlider?: boolean;
}

interface SelectInputFieldProps extends BaseInputFieldProps {
  type: "select";
  value: string;
  onChange: (value: string) => void;
  options: Array<{ value: string; label: string }>;
  placeholder?: string;
}

interface SliderInputFieldProps extends BaseInputFieldProps {
  type: "slider";
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  showValue?: boolean;
  formatValue?: (value: number) => string;
}

type InputFieldProps =
  | NumberInputFieldProps
  | CurrencyInputFieldProps
  | PercentageInputFieldProps
  | SelectInputFieldProps
  | SliderInputFieldProps;

export function InputField(props: InputFieldProps) {
  const { label, description, error, required, className } = props;

  const renderInput = () => {
    switch (props.type) {
      case "number":
        return (
          <div className="relative">
            <Input
              type="number"
              value={props.value || ""}
              onChange={(e) => props.onChange(parseFloat(e.target.value) || 0)}
              min={props.min}
              max={props.max}
              step={props.step}
              placeholder={props.placeholder}
              className={cn(error && "border-red-500")}
            />
            {props.suffix && (
              <span className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm">
                {props.suffix}
              </span>
            )}
          </div>
        );

      case "currency":
        return (
          <div className="flex gap-2">
            <div className="flex-1">
              <CurrencyInput
                value={props.value}
                onValueChange={(value) =>
                  props.onChange(parseFloat(value || "0"))
                }
                placeholder={props.placeholder || "0.00"}
                decimalsLimit={2}
                className={cn(
                  "border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
                  error && "border-red-500",
                )}
                min={props.min}
                max={props.max}
              />
            </div>
            {props.showCurrencySelector && props.onCurrencyChange && (
              <Select
                value={props.currency}
                onValueChange={props.onCurrencyChange}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getAllCurrencies().map((currency: CurrencyInfo) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.code}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        );

      case "percentage":
        return (
          <div className="space-y-3">
            <div className="relative">
              <Input
                type="number"
                value={props.value || ""}
                onChange={(e) =>
                  props.onChange(parseFloat(e.target.value) || 0)
                }
                min={props.min}
                max={props.max}
                step={props.step || 0.1}
                className={cn(error && "border-red-500")}
              />
              <span className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm">
                %
              </span>
            </div>
            {props.showSlider && (
              <Slider
                value={[props.value]}
                onValueChange={(values) => props.onChange(values[0])}
                min={props.min || 0}
                max={props.max || 100}
                step={props.step || 0.1}
                className="w-full"
              />
            )}
          </div>
        );

      case "select":
        return (
          <Select value={props.value} onValueChange={props.onChange}>
            <SelectTrigger className={cn(error && "border-red-500")}>
              <SelectValue placeholder={props.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {props.options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "slider":
        return (
          <div className="space-y-3">
            <Slider
              value={[props.value]}
              onValueChange={(values) => props.onChange(values[0])}
              min={props.min}
              max={props.max}
              step={props.step || 1}
              className="w-full"
            />
            {props.showValue && (
              <div className="text-muted-foreground text-center text-sm">
                {props.formatValue
                  ? props.formatValue(props.value)
                  : props.value}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium">
        {label}
        {required && <span className="ml-1 text-red-500">*</span>}
      </Label>

      {description && (
        <p className="text-muted-foreground text-xs">{description}</p>
      )}

      {renderInput()}

      {error && <p className="text-xs text-red-500">{error}</p>}
    </div>
  );
}

// 专用的货币输入组件
export function CurrencyInputField({
  label,
  value,
  onChange,
  currency,
  onCurrencyChange,
  showCurrencySelector = false,
  error,
  description,
  required,
  className,
  ...props
}: Omit<CurrencyInputFieldProps, "type">) {
  return (
    <InputField
      type="currency"
      label={label}
      value={value}
      onChange={onChange}
      currency={currency}
      onCurrencyChange={onCurrencyChange}
      showCurrencySelector={showCurrencySelector}
      error={error}
      description={description}
      required={required}
      className={className}
      {...props}
    />
  );
}

// 专用的百分比输入组件
export function PercentageInputField({
  label,
  value,
  onChange,
  showSlider = false,
  error,
  description,
  required,
  className,
  ...props
}: Omit<PercentageInputFieldProps, "type">) {
  return (
    <InputField
      type="percentage"
      label={label}
      value={value}
      onChange={onChange}
      showSlider={showSlider}
      error={error}
      description={description}
      required={required}
      className={className}
      {...props}
    />
  );
}
