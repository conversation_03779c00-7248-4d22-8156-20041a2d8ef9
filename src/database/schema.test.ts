import { describe, it, expect } from "@jest/globals";

// Import schema objects directly
import {
  userRoleEnum,
  users,
  sessions,
  accounts,
  verifications,
  subscriptions,
  payments,
  webhookEvents,
} from "./schema";

describe("Database Schema", () => {
  describe("userRoleEnum", () => {
    it("should export enum with correct values", () => {
      expect(userRoleEnum).toBeDefined();
      expect(userRoleEnum.enumValues).toEqual(["user", "admin", "super_admin"]);
    });

    it("should have valid role values", () => {
      const validRoles = ["user", "admin", "super_admin"];
      expect(userRoleEnum.enumValues).toEqual(
        expect.arrayContaining(validRoles),
      );
      expect(userRoleEnum.enumValues).toHaveLength(3);
    });

    it("should be an enum object with enumValues", () => {
      expect(userRoleEnum).toHaveProperty("enumValues");
      expect(Array.isArray(userRoleEnum.enumValues)).toBe(true);
    });
  });

  describe("Table Exports", () => {
    it("should export all table definitions", () => {
      expect(users).toBeDefined();
      expect(sessions).toBeDefined();
      expect(accounts).toBeDefined();
      expect(verifications).toBeDefined();
      expect(subscriptions).toBeDefined();
      expect(payments).toBeDefined();
      expect(webhookEvents).toBeDefined();
    });

    it("should have table objects with proper structure", () => {
      const tables = [
        users,
        sessions,
        accounts,
        verifications,
        subscriptions,
        payments,
        webhookEvents,
      ];

      tables.forEach((table) => {
        expect(table).toBeDefined();
        expect(typeof table).toBe("object");
      });
    });
  });

  describe("Users Table", () => {
    it("should have all required columns as properties", () => {
      expect(users).toHaveProperty("id");
      expect(users).toHaveProperty("name");
      expect(users).toHaveProperty("email");
      expect(users).toHaveProperty("emailVerified");
      expect(users).toHaveProperty("image");
      expect(users).toHaveProperty("role");
      expect(users).toHaveProperty("paymentProviderCustomerId");
      expect(users).toHaveProperty("createdAt");
      expect(users).toHaveProperty("updatedAt");
    });

    it("should have proper column constraints", () => {
      expect(users.id.primary).toBe(true);
      expect(users.name.notNull).toBe(true);
      expect(users.email.notNull).toBe(true);
      expect(users.emailVerified.notNull).toBe(true);
      expect(users.role.notNull).toBe(true);
      expect(users.createdAt.notNull).toBe(true);
      expect(users.updatedAt.notNull).toBe(true);
    });
  });

  describe("Sessions Table", () => {
    it("should have all required columns as properties", () => {
      expect(sessions).toHaveProperty("id");
      expect(sessions).toHaveProperty("expiresAt");
      expect(sessions).toHaveProperty("token");
      expect(sessions).toHaveProperty("createdAt");
      expect(sessions).toHaveProperty("updatedAt");
      expect(sessions).toHaveProperty("ipAddress");
      expect(sessions).toHaveProperty("userAgent");
      expect(sessions).toHaveProperty("os");
      expect(sessions).toHaveProperty("browser");
      expect(sessions).toHaveProperty("deviceType");
      expect(sessions).toHaveProperty("userId");
    });

    it("should have proper column constraints", () => {
      expect(sessions.id.primary).toBe(true);
      expect(sessions.expiresAt.notNull).toBe(true);
      expect(sessions.token.notNull).toBe(true);
      expect(sessions.createdAt.notNull).toBe(true);
      expect(sessions.updatedAt.notNull).toBe(true);
      expect(sessions.userId.notNull).toBe(true);
    });
  });

  describe("Accounts Table", () => {
    it("should have all required columns as properties", () => {
      expect(accounts).toHaveProperty("id");
      expect(accounts).toHaveProperty("accountId");
      expect(accounts).toHaveProperty("providerId");
      expect(accounts).toHaveProperty("userId");
      expect(accounts).toHaveProperty("accessToken");
      expect(accounts).toHaveProperty("refreshToken");
      expect(accounts).toHaveProperty("idToken");
      expect(accounts).toHaveProperty("accessTokenExpiresAt");
      expect(accounts).toHaveProperty("refreshTokenExpiresAt");
      expect(accounts).toHaveProperty("scope");
      expect(accounts).toHaveProperty("password");
      expect(accounts).toHaveProperty("createdAt");
      expect(accounts).toHaveProperty("updatedAt");
    });

    it("should have proper column constraints", () => {
      expect(accounts.id.primary).toBe(true);
      expect(accounts.accountId.notNull).toBe(true);
      expect(accounts.providerId.notNull).toBe(true);
      expect(accounts.userId.notNull).toBe(true);
      expect(accounts.createdAt.notNull).toBe(true);
      expect(accounts.updatedAt.notNull).toBe(true);
    });
  });

  describe("Verifications Table", () => {
    it("should have all required columns as properties", () => {
      expect(verifications).toHaveProperty("id");
      expect(verifications).toHaveProperty("identifier");
      expect(verifications).toHaveProperty("value");
      expect(verifications).toHaveProperty("expiresAt");
      expect(verifications).toHaveProperty("createdAt");
      expect(verifications).toHaveProperty("updatedAt");
    });

    it("should have proper column constraints", () => {
      expect(verifications.id.primary).toBe(true);
      expect(verifications.identifier.notNull).toBe(true);
      expect(verifications.value.notNull).toBe(true);
      expect(verifications.expiresAt.notNull).toBe(true);
    });
  });

  describe("Subscriptions Table", () => {
    it("should have all required columns as properties", () => {
      expect(subscriptions).toHaveProperty("id");
      expect(subscriptions).toHaveProperty("userId");
      expect(subscriptions).toHaveProperty("customerId");
      expect(subscriptions).toHaveProperty("subscriptionId");
      expect(subscriptions).toHaveProperty("productId");
      expect(subscriptions).toHaveProperty("status");
      expect(subscriptions).toHaveProperty("currentPeriodStart");
      expect(subscriptions).toHaveProperty("currentPeriodEnd");
      expect(subscriptions).toHaveProperty("canceledAt");
      expect(subscriptions).toHaveProperty("createdAt");
      expect(subscriptions).toHaveProperty("updatedAt");
    });

    it("should have proper column constraints", () => {
      expect(subscriptions.id.primary).toBe(true);
      expect(subscriptions.userId.notNull).toBe(true);
      expect(subscriptions.customerId.notNull).toBe(true);
      expect(subscriptions.subscriptionId.notNull).toBe(true);
      expect(subscriptions.productId.notNull).toBe(true);
      expect(subscriptions.status.notNull).toBe(true);
      expect(subscriptions.createdAt.notNull).toBe(true);
      expect(subscriptions.updatedAt.notNull).toBe(true);
    });
  });

  describe("Payments Table", () => {
    it("should have all required columns as properties", () => {
      expect(payments).toHaveProperty("id");
      expect(payments).toHaveProperty("userId");
      expect(payments).toHaveProperty("customerId");
      expect(payments).toHaveProperty("subscriptionId");
      expect(payments).toHaveProperty("productId");
      expect(payments).toHaveProperty("paymentId");
      expect(payments).toHaveProperty("amount");
      expect(payments).toHaveProperty("currency");
      expect(payments).toHaveProperty("status");
      expect(payments).toHaveProperty("paymentType");
      expect(payments).toHaveProperty("createdAt");
      expect(payments).toHaveProperty("updatedAt");
    });

    it("should have proper column constraints", () => {
      expect(payments.id.primary).toBe(true);
      expect(payments.userId.notNull).toBe(true);
      expect(payments.customerId.notNull).toBe(true);
      expect(payments.productId.notNull).toBe(true);
      expect(payments.paymentId.notNull).toBe(true);
      expect(payments.amount.notNull).toBe(true);
      expect(payments.currency.notNull).toBe(true);
      expect(payments.status.notNull).toBe(true);
      expect(payments.paymentType.notNull).toBe(true);
      expect(payments.createdAt.notNull).toBe(true);
      expect(payments.updatedAt.notNull).toBe(true);
    });
  });

  describe("Webhook Events Table", () => {
    it("should have all required columns as properties", () => {
      expect(webhookEvents).toHaveProperty("id");
      expect(webhookEvents).toHaveProperty("eventId");
      expect(webhookEvents).toHaveProperty("eventType");
      expect(webhookEvents).toHaveProperty("provider");
      expect(webhookEvents).toHaveProperty("processed");
      expect(webhookEvents).toHaveProperty("processedAt");
      expect(webhookEvents).toHaveProperty("payload");
      expect(webhookEvents).toHaveProperty("createdAt");
    });

    it("should have proper column constraints", () => {
      expect(webhookEvents.id.primary).toBe(true);
      expect(webhookEvents.eventId.notNull).toBe(true);
      expect(webhookEvents.eventType.notNull).toBe(true);
      expect(webhookEvents.provider.notNull).toBe(true);
      expect(webhookEvents.processed.notNull).toBe(true);
      expect(webhookEvents.processedAt.notNull).toBe(true);
      expect(webhookEvents.createdAt.notNull).toBe(true);
    });
  });

  describe("Schema Relationships", () => {
    it("should have foreign key relationships defined", () => {
      // Check sessions table has userId foreign key
      expect(sessions.userId.name).toBe("userId");
      expect(sessions.userId.notNull).toBe(true);

      // Check accounts table has userId foreign key
      expect(accounts.userId.name).toBe("userId");
      expect(accounts.userId.notNull).toBe(true);

      // Check subscriptions table has userId foreign key
      expect(subscriptions.userId.name).toBe("userId");
      expect(subscriptions.userId.notNull).toBe(true);

      // Check payments table has userId foreign key
      expect(payments.userId.name).toBe("userId");
      expect(payments.userId.notNull).toBe(true);
    });

    it("should have proper primary keys", () => {
      expect(users.id.primary).toBe(true);
      expect(sessions.id.primary).toBe(true);
      expect(accounts.id.primary).toBe(true);
      expect(verifications.id.primary).toBe(true);
      expect(subscriptions.id.primary).toBe(true);
      expect(payments.id.primary).toBe(true);
      expect(webhookEvents.id.primary).toBe(true);
    });
  });

  describe("Business Logic Support", () => {
    it("should support authentication", () => {
      // Should have users, sessions, accounts, verifications tables
      expect(users).toBeDefined();
      expect(sessions).toBeDefined();
      expect(accounts).toBeDefined();
      expect(verifications).toBeDefined();

      // Should have required auth columns
      expect(users.email).toBeDefined();
      expect(users.emailVerified).toBeDefined();
      expect(sessions.token).toBeDefined();
      expect(accounts.providerId).toBeDefined();
    });

    it("should support payment processing", () => {
      // Should have subscriptions, payments, webhookEvents tables
      expect(subscriptions).toBeDefined();
      expect(payments).toBeDefined();
      expect(webhookEvents).toBeDefined();

      // Should have required payment columns
      expect(payments.amount).toBeDefined();
      expect(payments.currency).toBeDefined();
      expect(payments.status).toBeDefined();
    });
  });
});
