import { getTableConfig, AnyPgColumn, PgTable } from "drizzle-orm/pg-core";
import { z } from "zod";
import { type EnabledTableKeys } from "@/lib/config/admin-tables";
import { adminTableConfig } from "./config";
import { type ColumnType, type SchemaInfo } from "./types";
import { users } from "@/database/schema";

export { type ColumnType, type ColumnInfo, type SchemaInfo } from "./types";

const drizzleTypeToTsType: Record<string, ColumnType> = {
  PgText: "string",
  PgVarchar: "string",
  PgChar: "string",
  PgInteger: "number",
  PgBigInt53: "number",
  PgBoolean: "boolean",
  PgTimestamp: "date",
  PgDate: "date",
  PgEnum: "enum",
  PgJsonb: "json",
  PgUUID: "uuid",
};

export function getTableSchema(
  table: PgTable,
  tableName: EnabledTableKeys,
): SchemaInfo {
  const tableDetails = getTableConfig(table);
  const config = adminTableConfig[tableName] ?? {};
  const userRelatedColumn =
    typeof config.userRelated === "string"
      ? config.userRelated
      : config.userRelated
        ? "userId"
        : null;
  const userTableName = getTableConfig(users).name;

  return tableDetails.columns.map((column: AnyPgColumn) => {
    let columnType: ColumnType =
      drizzleTypeToTsType[column.dataType] || "string";

    const foreignKey = tableDetails.foreignKeys.find((fk) =>
      fk.reference().columns.some((c: AnyPgColumn) => c.name === column.name),
    );

    const foreignTableName = foreignKey
      ? getTableConfig(foreignKey.reference().foreignTable).name
      : undefined;
    const isUserIdFk = foreignTableName === userTableName;

    if (column.name === userRelatedColumn && isUserIdFk) {
      columnType = "user_id";
    } else if (
      // FIX: Use getSQLType() which returns a string literal like 'text'
      column.getSQLType() === "text" &&
      (column.name.includes("description") || column.name.includes("content"))
    ) {
      columnType = "text";
    }

    const isPrimaryKey = column.primary;
    const isOptional = !column.notNull || column.hasDefault || isPrimaryKey;

    return {
      name: column.name,
      type: columnType,
      isOptional,
      isPrimaryKey,
      isAutoGenerated: column.hasDefault && isPrimaryKey,
      enumValues:
        "enumValues" in column ? (column.enumValues as string[]) : undefined,
    };
  });
}

export function generateZodSchema(
  schemaInfo: SchemaInfo,
  readOnlyColumns: string[] = [],
): z.ZodObject<z.ZodRawShape> {
  const shape: Record<string, z.ZodType> = {};

  schemaInfo.forEach((col) => {
    if (col.isAutoGenerated || readOnlyColumns.includes(col.name)) return;

    let fieldSchema: z.ZodType;

    switch (col.type) {
      case "string":
      case "uuid":
      case "text":
      case "user_id":
        fieldSchema = z.string();
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "number":
        fieldSchema = z.coerce.number();
        break;
      case "boolean":
        fieldSchema = z.boolean().default(false);
        break;
      case "date":
        fieldSchema = z.preprocess((arg) => {
          if (typeof arg == "string" && arg) return new Date(arg);
          if (!arg) return undefined;
          return arg;
        }, z.date().optional());
        break;
      case "enum":
        if (col.enumValues && col.enumValues.length > 0) {
          fieldSchema = z.enum(col.enumValues as [string, ...string[]]);
        } else {
          fieldSchema = z.string();
        }
        break;
      case "json":
        fieldSchema = z
          .string()
          .transform((str, ctx) => {
            if (!str) return null;
            try {
              return JSON.parse(str);
            } catch {
              ctx.addIssue({ code: "custom", message: "Invalid JSON" });
              return z.NEVER;
            }
          })
          .pipe(z.any());
        break;
      default:
        // This should not happen with the current types, but as a fallback:
        fieldSchema = z.any();
    }

    if (col.isOptional) {
      shape[col.name] = fieldSchema.optional().nullable();
    } else {
      shape[col.name] = fieldSchema;
    }
  });

  return z.object(shape);
}
