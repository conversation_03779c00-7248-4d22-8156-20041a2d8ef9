"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DollarSign,
  TrendingUp,
  Target,
  Calendar,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  LineChart,
  Pie<PERSON>hart,
  Settings,
  Plus,
} from "lucide-react";
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  Legend,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 类型定义（从主文件导入）
interface InvestmentStrategy {
  id: string;
  name: string;
  type: "lump_sum" | "dca";
  currency: SupportedCurrency;
  isVisible: boolean;
  color: string;
  params: {
    initialAmount?: number;
    monthlyAmount?: number;
    expectedReturn: number;
    timeHorizon: number;
  };
  records?: any[];
  result?: {
    currentValue: number;
    totalInvested: number;
    totalGains: number;
    annualizedReturn: number;
  };
}

interface FinancialGoal {
  id: string;
  name: string;
  type: "retirement" | "savings_goal";
  targetAmount: number;
  targetDate: Date;
  currentProgress: number;
  currency: SupportedCurrency;
  isActive: boolean;
}

interface Portfolio {
  id: string;
  name: string;
  displayCurrency: SupportedCurrency;
  strategies: InvestmentStrategy[];
  goals: FinancialGoal[];
  createdAt: Date;
  updatedAt: Date;
}

// Overview View - 主要的图表和概览
export function OverviewView({
  portfolio,
  chartData,
  chartSettings,
  onChartSettingsChange,
  onToggleStrategyVisibility,
  displayCurrency,
}: {
  portfolio: Portfolio;
  chartData: any[];
  chartSettings: any;
  onChartSettingsChange: (settings: any) => void;
  onToggleStrategyVisibility: (strategyId: string) => void;
  displayCurrency: SupportedCurrency;
}) {
  // 计算总体统计
  const totalValue = portfolio.strategies.reduce(
    (sum, strategy) => sum + (strategy.result?.currentValue || 0),
    0,
  );
  const totalInvested = portfolio.strategies.reduce(
    (sum, strategy) => sum + (strategy.result?.totalInvested || 0),
    0,
  );
  const totalGains = totalValue - totalInvested;
  const totalReturn =
    totalInvested > 0 ? (totalGains / totalInvested) * 100 : 0;

  return (
    <div className="space-y-8">
      {/* Portfolio Summary */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="text-muted-foreground h-4 w-4" />
              <span className="ml-2 text-sm font-medium">Total Value</span>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">
                {formatCurrency(totalValue, displayCurrency)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="text-muted-foreground h-4 w-4" />
              <span className="ml-2 text-sm font-medium">Total Gains</span>
            </div>
            <div className="mt-2">
              <div
                className={`text-2xl font-bold ${totalGains >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {formatCurrency(totalGains, displayCurrency)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="text-muted-foreground h-4 w-4" />
              <span className="ml-2 text-sm font-medium">Total Return</span>
            </div>
            <div className="mt-2">
              <div
                className={`text-2xl font-bold ${totalReturn >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {CurrencyUtils.formatPercentage(totalReturn)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Target className="text-muted-foreground h-4 w-4" />
              <span className="ml-2 text-sm font-medium">Active Goals</span>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">
                {portfolio.goals.filter((g) => g.isActive).length}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chart Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <LineChart className="h-5 w-5" />
              Portfolio Performance
            </CardTitle>
            <div className="flex items-center gap-2">
              <Select
                value={chartSettings.timeRange}
                onValueChange={(value) =>
                  onChartSettingsChange({ ...chartSettings, timeRange: value })
                }
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1y">1Y</SelectItem>
                  <SelectItem value="3y">3Y</SelectItem>
                  <SelectItem value="5y">5Y</SelectItem>
                  <SelectItem value="10y">10Y</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={chartSettings.chartType}
                onValueChange={(value) =>
                  onChartSettingsChange({ ...chartSettings, chartType: value })
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="area">Area Chart</SelectItem>
                  <SelectItem value="line">Line Chart</SelectItem>
                  <SelectItem value="bar">Bar Chart</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Strategy Visibility Controls */}
          <div className="mb-4 flex flex-wrap gap-2">
            {portfolio.strategies.map((strategy) => (
              <Button
                key={strategy.id}
                variant={strategy.isVisible ? "default" : "outline"}
                size="sm"
                onClick={() => onToggleStrategyVisibility(strategy.id)}
                className="flex items-center gap-2"
              >
                <div
                  className="h-3 w-3 rounded-full"
                  style={{ backgroundColor: strategy.color }}
                />
                {strategy.name}
                {strategy.isVisible ? (
                  <Eye className="h-3 w-3" />
                ) : (
                  <EyeOff className="h-3 w-3" />
                )}
              </Button>
            ))}
          </div>

          {/* Chart */}
          <ResponsiveContainer width="100%" height={400}>
            {chartSettings.chartType === "area" ? (
              <AreaChart
                data={generateMockChartData(
                  portfolio.strategies.filter((s) => s.isVisible),
                )}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) =>
                    formatCurrency(value, displayCurrency)
                  }
                />
                <Legend />
                {portfolio.strategies
                  .filter((s) => s.isVisible)
                  .map((strategy) => (
                    <Area
                      key={strategy.id}
                      type="monotone"
                      dataKey={strategy.name}
                      stackId="1"
                      stroke={strategy.color}
                      fill={strategy.color}
                      fillOpacity={0.6}
                    />
                  ))}
              </AreaChart>
            ) : chartSettings.chartType === "line" ? (
              <RechartsLineChart
                data={generateMockChartData(
                  portfolio.strategies.filter((s) => s.isVisible),
                )}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) =>
                    formatCurrency(value, displayCurrency)
                  }
                />
                <Legend />
                {portfolio.strategies
                  .filter((s) => s.isVisible)
                  .map((strategy) => (
                    <Line
                      key={strategy.id}
                      type="monotone"
                      dataKey={strategy.name}
                      stroke={strategy.color}
                      strokeWidth={2}
                    />
                  ))}
              </RechartsLineChart>
            ) : (
              <BarChart
                data={generateMockChartData(
                  portfolio.strategies.filter((s) => s.isVisible),
                )}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) =>
                    formatCurrency(value, displayCurrency)
                  }
                />
                <Legend />
                {portfolio.strategies
                  .filter((s) => s.isVisible)
                  .map((strategy) => (
                    <Bar
                      key={strategy.id}
                      dataKey={strategy.name}
                      fill={strategy.color}
                    />
                  ))}
              </BarChart>
            )}
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Strategy Allocation Pie Chart */}
      <div className="grid gap-8 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Strategy Allocation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={portfolio.strategies.map((s) => ({
                    name: s.name,
                    value: s.result?.currentValue || 0,
                    color: s.color,
                  }))}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                >
                  {portfolio.strategies.map((strategy, index) => (
                    <Cell key={`cell-${index}`} fill={strategy.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) =>
                    formatCurrency(value, displayCurrency)
                  }
                />
                <Legend />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Financial Goals Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {portfolio.goals
                .filter((g) => g.isActive)
                .map((goal) => {
                  const progress =
                    (goal.currentProgress / goal.targetAmount) * 100;
                  const timeLeft = Math.ceil(
                    (goal.targetDate.getTime() - new Date().getTime()) /
                      (1000 * 60 * 60 * 24 * 365),
                  );

                  return (
                    <div key={goal.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{goal.name}</span>
                        <span className="text-muted-foreground text-sm">
                          {timeLeft} years left
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-blue-600"
                          style={{ width: `${Math.min(progress, 100)}%` }}
                        />
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>
                          {formatCurrency(
                            goal.currentProgress,
                            displayCurrency,
                          )}
                        </span>
                        <span>
                          {formatCurrency(goal.targetAmount, displayCurrency)}
                        </span>
                      </div>
                      <div className="text-muted-foreground text-center text-sm">
                        {progress.toFixed(1)}% complete
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// 生成模拟图表数据的辅助函数
function generateMockChartData(strategies: InvestmentStrategy[]): any[] {
  const months = 24; // 2年数据
  const data = [];

  for (let i = 0; i < months; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - (months - 1 - i));

    const dataPoint: any = {
      date: date.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      }),
    };

    strategies.forEach((strategy) => {
      // 模拟增长数据
      const monthlyGrowth = Math.pow(
        1 + strategy.params.expectedReturn / 100 / 12,
        i,
      );
      const baseValue = strategy.params.initialAmount || 0;
      const monthlyContribution = strategy.params.monthlyAmount || 0;

      if (strategy.type === "dca") {
        dataPoint[strategy.name] =
          baseValue * monthlyGrowth + monthlyContribution * i * monthlyGrowth;
      } else {
        dataPoint[strategy.name] = baseValue * monthlyGrowth;
      }
    });

    data.push(dataPoint);
  }

  return data;
}

// Strategies View - 投资策略管理
export function StrategiesView({
  strategies,
  displayCurrency,
  onEdit,
  onDelete,
  onToggleVisibility,
}: {
  strategies: InvestmentStrategy[];
  displayCurrency: SupportedCurrency;
  onEdit: (strategy: InvestmentStrategy) => void;
  onDelete: (strategyId: string) => void;
  onToggleVisibility: (strategyId: string) => void;
}) {
  return (
    <div className="space-y-6">
      {/* Strategies Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Investment Strategies
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Strategy</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Currency</TableHead>
                <TableHead>Expected Return</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Total Gains</TableHead>
                <TableHead>Visibility</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {strategies.map((strategy) => (
                <TableRow key={strategy.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: strategy.color }}
                      />
                      <span className="font-medium">{strategy.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        strategy.type === "dca" ? "default" : "secondary"
                      }
                    >
                      {strategy.type === "dca"
                        ? "Dollar Cost Averaging"
                        : "Lump Sum"}
                    </Badge>
                  </TableCell>
                  <TableCell>{strategy.currency}</TableCell>
                  <TableCell>
                    {CurrencyUtils.formatPercentage(
                      strategy.params.expectedReturn,
                    )}
                  </TableCell>
                  <TableCell>
                    {strategy.result
                      ? formatCurrency(
                          strategy.result.currentValue,
                          strategy.currency,
                        )
                      : "-"}
                  </TableCell>
                  <TableCell>
                    {strategy.result ? (
                      <span
                        className={
                          strategy.result.totalGains >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {formatCurrency(
                          strategy.result.totalGains,
                          strategy.currency,
                        )}
                      </span>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleVisibility(strategy.id)}
                    >
                      {strategy.isVisible ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(strategy)}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(strategy.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* DCA Strategies with Records */}
      {strategies
        .filter((s) => s.type === "dca")
        .map((strategy) => (
          <Card key={`dca-${strategy.id}`}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {strategy.name} - Investment Records
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Records Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Total Records
                    </p>
                    <p className="text-lg font-semibold">
                      {strategy.records?.length || 0}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Total Invested
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(
                        strategy.records?.reduce(
                          (sum, record) => sum + record.amount,
                          0,
                        ) || 0,
                        strategy.currency,
                      )}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Average Amount
                    </p>
                    <p className="text-lg font-semibold">
                      {strategy.records?.length
                        ? formatCurrency(
                            strategy.records.reduce(
                              (sum, record) => sum + record.amount,
                              0,
                            ) / strategy.records.length,
                            strategy.currency,
                          )
                        : "-"}
                    </p>
                  </div>
                </div>

                {/* Recent Records */}
                {strategy.records && strategy.records.length > 0 && (
                  <div>
                    <h5 className="mb-2 font-medium">Recent Investments</h5>
                    <div className="space-y-2">
                      {strategy.records.slice(-5).map((record) => (
                        <div
                          key={record.id}
                          className="bg-muted/50 flex items-center justify-between rounded p-2"
                        >
                          <div>
                            <span className="font-medium">
                              {new Date(record.date).toLocaleDateString()}
                            </span>
                            {record.note && (
                              <span className="text-muted-foreground ml-2 text-sm">
                                - {record.note}
                              </span>
                            )}
                          </div>
                          <span className="font-medium">
                            {formatCurrency(record.amount, strategy.currency)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button variant="outline" size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Investment Record
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
    </div>
  );
}

// Goals View - 财务目标管理
export function GoalsView({
  goals,
  displayCurrency,
  onEdit,
}: {
  goals: FinancialGoal[];
  displayCurrency: SupportedCurrency;
  onEdit: (goal: FinancialGoal) => void;
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Financial Goals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {goals.map((goal) => {
              const progress = (goal.currentProgress / goal.targetAmount) * 100;
              const timeLeft = Math.ceil(
                (goal.targetDate.getTime() - new Date().getTime()) /
                  (1000 * 60 * 60 * 24 * 365),
              );
              const monthlyNeeded =
                timeLeft > 0
                  ? (goal.targetAmount - goal.currentProgress) / (timeLeft * 12)
                  : 0;

              return (
                <Card key={goal.id} className="relative">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{goal.name}</CardTitle>
                        <Badge
                          variant={
                            goal.type === "retirement" ? "default" : "secondary"
                          }
                        >
                          {goal.type === "retirement"
                            ? "Retirement"
                            : "Savings Goal"}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(goal)}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Progress Bar */}
                      <div>
                        <div className="mb-2 flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{progress.toFixed(1)}%</span>
                        </div>
                        <div className="h-3 w-full rounded-full bg-gray-200">
                          <div
                            className="h-3 rounded-full bg-blue-600 transition-all duration-300"
                            style={{ width: `${Math.min(progress, 100)}%` }}
                          />
                        </div>
                      </div>

                      {/* Goal Details */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Current</p>
                          <p className="font-semibold">
                            {formatCurrency(
                              goal.currentProgress,
                              goal.currency,
                            )}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Target</p>
                          <p className="font-semibold">
                            {formatCurrency(goal.targetAmount, goal.currency)}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Time Left</p>
                          <p className="font-semibold">{timeLeft} years</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">
                            Monthly Needed
                          </p>
                          <p className="font-semibold">
                            {formatCurrency(monthlyNeeded, goal.currency)}
                          </p>
                        </div>
                      </div>

                      {/* Target Date */}
                      <div className="text-sm">
                        <p className="text-muted-foreground">Target Date</p>
                        <p className="font-medium">
                          {goal.targetDate.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
