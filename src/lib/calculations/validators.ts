import { z } from "zod";
import {
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
  CalculationValidationResult,
  ValidationError,
  SupportedCurrency,
} from "./types";

// 支持的货币列表
const supportedCurrencies: SupportedCurrency[] = [
  "USD",
  "EUR",
  "GBP",
  "JPY",
  "CNY",
  "KRW",
  "CAD",
  "AUD",
];

// 基础验证规则
const baseSchema = z.object({
  currency: z.enum(
    supportedCurrencies as [SupportedCurrency, ...SupportedCurrency[]],
  ),
  name: z.string().optional(),
});

// 一次性投资验证
const lumpSumSchema = baseSchema.extend({
  principal: z
    .number()
    .min(0.01, "Initial investment must be greater than 0")
    .max(1000000000, "Initial investment cannot exceed $1 billion"),
  annualRate: z
    .number()
    .min(-50, "Annual return cannot be less than -50%")
    .max(100, "Annual return cannot exceed 100%"),
  years: z
    .number()
    .min(0.1, "Investment period must be at least 0.1 years")
    .max(100, "Investment period cannot exceed 100 years"),
  compoundingFrequency: z
    .number()
    .min(1, "Compounding frequency must be at least 1")
    .max(365, "Compounding frequency cannot exceed 365")
    .optional()
    .default(12),
});

// 定投验证
const dcaSchema = baseSchema.extend({
  initialInvestment: z
    .number()
    .min(0, "Initial investment cannot be negative")
    .max(1000000000, "Initial investment cannot exceed $1 billion")
    .optional()
    .default(0),
  monthlyContribution: z
    .number()
    .min(0.01, "Monthly contribution must be greater than 0")
    .max(1000000, "Monthly contribution cannot exceed $1 million"),
  annualRate: z
    .number()
    .min(-50, "Annual return cannot be less than -50%")
    .max(100, "Annual return cannot exceed 100%"),
  years: z
    .number()
    .min(0.1, "Investment period must be at least 0.1 years")
    .max(100, "Investment period cannot exceed 100 years"),
  contributionIncrease: z
    .number()
    .min(0, "Contribution increase cannot be negative")
    .max(50, "Contribution increase cannot exceed 50%")
    .optional()
    .default(0),
});

// 退休规划验证
const retirementSchema = baseSchema.extend({
  currentAge: z
    .number()
    .min(18, "Current age must be at least 18")
    .max(100, "Current age cannot exceed 100"),
  retirementAge: z
    .number()
    .min(50, "Retirement age must be at least 50")
    .max(120, "Retirement age cannot exceed 120"),
  currentSavings: z
    .number()
    .min(0, "Current savings cannot be negative")
    .max(1000000000, "Current savings cannot exceed $1 billion"),
  monthlyContribution: z
    .number()
    .min(0, "Monthly contribution cannot be negative")
    .max(1000000, "Monthly contribution cannot exceed $1 million"),
  expectedReturn: z
    .number()
    .min(-50, "Expected return cannot be less than -50%")
    .max(100, "Expected return cannot exceed 100%"),
  inflationRate: z
    .number()
    .min(0, "Inflation rate cannot be negative")
    .max(50, "Inflation rate cannot exceed 50%")
    .optional()
    .default(2.5),
  retirementGoal: z
    .number()
    .min(1, "Retirement goal must be greater than 0")
    .max(1000000000, "Retirement goal cannot exceed $1 billion"),
});

// 储蓄目标验证
const savingsGoalSchema = baseSchema.extend({
  targetAmount: z
    .number()
    .min(1, "Target amount must be greater than 0")
    .max(1000000000, "Target amount cannot exceed $1 billion"),
  timeframe: z
    .number()
    .min(0.1, "Timeframe must be at least 0.1 years")
    .max(100, "Timeframe cannot exceed 100 years"),
  initialAmount: z
    .number()
    .min(0, "Initial amount cannot be negative")
    .max(1000000000, "Initial amount cannot exceed $1 billion")
    .optional()
    .default(0),
  expectedReturn: z
    .number()
    .min(-50, "Expected return cannot be less than -50%")
    .max(100, "Expected return cannot exceed 100%"),
  contributionFrequency: z.enum(["monthly", "quarterly", "annually"]),
});

// 验证函数
export class CalculationValidator {
  static validateLumpSum(params: LumpSumParams): CalculationValidationResult {
    try {
      lumpSumSchema.parse(params);
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        }));
        return { isValid: false, errors };
      }
      return {
        isValid: false,
        errors: [{ field: "general", message: "Validation failed" }],
      };
    }
  }

  static validateDCA(params: DCAParams): CalculationValidationResult {
    try {
      dcaSchema.parse(params);
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        }));
        return { isValid: false, errors };
      }
      return {
        isValid: false,
        errors: [{ field: "general", message: "Validation failed" }],
      };
    }
  }

  static validateRetirement(
    params: RetirementParams,
  ): CalculationValidationResult {
    try {
      const result = retirementSchema.parse(params);

      // 额外的业务逻辑验证
      const errors: ValidationError[] = [];

      if (result.retirementAge <= result.currentAge) {
        errors.push({
          field: "retirementAge",
          message: "Retirement age must be greater than current age",
        });
      }

      if (errors.length > 0) {
        return { isValid: false, errors };
      }

      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        }));
        return { isValid: false, errors };
      }
      return {
        isValid: false,
        errors: [{ field: "general", message: "Validation failed" }],
      };
    }
  }

  static validateSavingsGoal(
    params: SavingsGoalParams,
  ): CalculationValidationResult {
    try {
      const result = savingsGoalSchema.parse(params);

      // 额外的业务逻辑验证
      const errors: ValidationError[] = [];

      if (result.initialAmount && result.initialAmount >= result.targetAmount) {
        errors.push({
          field: "targetAmount",
          message: "Target amount must be greater than initial amount",
        });
      }

      if (errors.length > 0) {
        return { isValid: false, errors };
      }

      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        }));
        return { isValid: false, errors };
      }
      return {
        isValid: false,
        errors: [{ field: "general", message: "Validation failed" }],
      };
    }
  }

  // 通用验证方法
  static validate(type: string, params: unknown): CalculationValidationResult {
    switch (type) {
      case "lump_sum":
        return this.validateLumpSum(params as LumpSumParams);
      case "dca":
        return this.validateDCA(params as DCAParams);
      case "retirement":
        return this.validateRetirement(params as RetirementParams);
      case "savings_goal":
        return this.validateSavingsGoal(params as SavingsGoalParams);
      default:
        return {
          isValid: false,
          errors: [{ field: "type", message: "Invalid calculation type" }],
        };
    }
  }
}

// 导出验证模式供其他地方使用
export {
  lumpSumSchema,
  dcaSchema,
  retirementSchema,
  savingsGoalSchema,
  supportedCurrencies,
};
