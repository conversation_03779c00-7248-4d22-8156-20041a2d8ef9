"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Edit2,
  Trash2,
  Plus,
  Save,
  X,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { CurrencyInputField } from "@/components/calculator/core/InputField";

// 类型定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  currency: string;
  color: string;
  records: InvestmentRecord[];
  isVisibleInComparison: boolean;
  currentPrice: number;
  lastUpdated: Date;
  totalInvested: number;
  currentValue: number;
  totalShares: number;
  averageCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
}

// 一体化Holdings管理器
export function IntegratedHoldingManager({
  holding,
  onSave,
  onCancel,
  isNew = false,
}: {
  holding: InvestmentHolding;
  onSave: (holding: InvestmentHolding) => void;
  onCancel: () => void;
  isNew?: boolean;
}) {
  const [editedHolding, setEditedHolding] =
    useState<InvestmentHolding>(holding);
  const [activeTab, setActiveTab] = useState<"basic" | "records" | "analysis">(
    "basic",
  );
  const [editingRecord, setEditingRecord] = useState<InvestmentRecord | null>(
    null,
  );
  const [isAddingRecord, setIsAddingRecord] = useState(false);

  // 更新基本信息
  const updateBasicInfo = (field: string, value: any) => {
    setEditedHolding((prev) => ({
      ...prev,
      [field]: value,
      lastUpdated: field === "currentPrice" ? new Date() : prev.lastUpdated,
    }));
  };

  // 添加投资记录
  const addRecord = (recordData: Partial<InvestmentRecord>) => {
    const newRecord: InvestmentRecord = {
      id: Date.now().toString(),
      date: recordData.date || new Date(),
      amount: recordData.amount || 0,
      price: recordData.price,
      shares: recordData.shares,
      fees: recordData.fees || 0,
      note: recordData.note,
      type: recordData.type || "buy",
    };

    const updatedRecords = [...editedHolding.records, newRecord];
    const updatedHolding = {
      ...editedHolding,
      records: updatedRecords,
      ...calculateStats(updatedRecords, editedHolding.currentPrice),
    };

    setEditedHolding(updatedHolding);
    setIsAddingRecord(false);
  };

  // 更新投资记录
  const updateRecord = (
    recordId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    const updatedRecords = editedHolding.records.map((r) =>
      r.id === recordId ? { ...r, ...recordData } : r,
    );

    const updatedHolding = {
      ...editedHolding,
      records: updatedRecords,
      ...calculateStats(updatedRecords, editedHolding.currentPrice),
    };

    setEditedHolding(updatedHolding);
    setEditingRecord(null);
  };

  // 删除投资记录
  const deleteRecord = (recordId: string) => {
    const updatedRecords = editedHolding.records.filter(
      (r) => r.id !== recordId,
    );
    const updatedHolding = {
      ...editedHolding,
      records: updatedRecords,
      ...calculateStats(updatedRecords, editedHolding.currentPrice),
    };

    setEditedHolding(updatedHolding);
  };

  // 计算统计数据
  const calculateStats = (
    records: InvestmentRecord[],
    currentPrice: number,
  ) => {
    const buyRecords = records.filter((r) => r.type === "buy");
    const sellRecords = records.filter((r) => r.type === "sell");

    const totalInvested = buyRecords.reduce((sum, r) => sum + r.amount, 0);
    const totalBoughtShares = buyRecords.reduce(
      (sum, r) => sum + (r.shares || 0),
      0,
    );
    const totalSoldShares = sellRecords.reduce(
      (sum, r) => sum + (r.shares || 0),
      0,
    );
    const currentShares = totalBoughtShares - totalSoldShares;

    const averageCost =
      totalBoughtShares > 0 ? totalInvested / totalBoughtShares : 0;
    const currentValue = currentShares * currentPrice;
    const costBasis = currentShares * averageCost;
    const unrealizedGain = currentValue - costBasis;
    const unrealizedGainPercent =
      costBasis > 0 ? (unrealizedGain / costBasis) * 100 : 0;

    return {
      totalInvested,
      currentValue,
      totalShares: currentShares,
      averageCost,
      unrealizedGain,
      unrealizedGainPercent,
    };
  };

  // 当价格变化时重新计算
  React.useEffect(() => {
    const stats = calculateStats(
      editedHolding.records,
      editedHolding.currentPrice,
    );
    setEditedHolding((prev) => ({ ...prev, ...stats }));
  }, [editedHolding.currentPrice]);

  const handleSave = () => {
    onSave(editedHolding);
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="m-0 flex h-[100dvh] max-h-none w-[100vw] max-w-none flex-col overflow-hidden rounded-none p-0 sm:m-4 sm:h-[90vh] sm:max-h-[90vh] sm:w-[95vw] sm:max-w-6xl sm:rounded-lg">
        <DialogHeader className="flex-shrink-0 border-b p-4 pb-3">
          <DialogTitle className="flex items-center gap-2 text-lg">
            <Target className="h-5 w-5" />
            <span className="truncate">
              {isNew
                ? "Add New Investment Holding"
                : `Manage ${editedHolding.name}`}
            </span>
          </DialogTitle>
          <DialogDescription className="text-muted-foreground mt-1 text-sm">
            {isNew
              ? "Create a new investment holding and add your investment records"
              : "Manage your investment holding details and records"}
          </DialogDescription>
        </DialogHeader>

        <div className="flex min-h-0 flex-1 flex-col overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as any)}
            className="flex min-h-0 flex-1 flex-col"
          >
            <TabsList className="mx-3 mt-2 mb-0 grid h-9 w-full flex-shrink-0 grid-cols-3">
              <TabsTrigger value="basic" className="h-8 px-2 text-sm">
                <span className="hidden sm:inline">Basic Info</span>
                <span className="sm:hidden">Basic</span>
              </TabsTrigger>
              <TabsTrigger value="records" className="h-8 px-2 text-sm">
                <span className="hidden sm:inline">Investment Records</span>
                <span className="sm:hidden">Records</span>
              </TabsTrigger>
              <TabsTrigger value="analysis" className="h-8 px-2 text-sm">
                Analysis
              </TabsTrigger>
            </TabsList>

            <div className="min-h-0 flex-1 overflow-y-auto">
              <TabsContent value="basic" className="m-0 h-full space-y-3 p-3">
                <BasicInfoTab
                  holding={editedHolding}
                  onUpdate={updateBasicInfo}
                />
              </TabsContent>

              <TabsContent value="records" className="m-0 h-full space-y-3 p-3">
                <RecordsTab
                  holding={editedHolding}
                  onAddRecord={addRecord}
                  onUpdateRecord={updateRecord}
                  onDeleteRecord={deleteRecord}
                  editingRecord={editingRecord}
                  setEditingRecord={setEditingRecord}
                  isAddingRecord={isAddingRecord}
                  setIsAddingRecord={setIsAddingRecord}
                />
              </TabsContent>

              <TabsContent
                value="analysis"
                className="m-0 h-full space-y-3 p-3"
              >
                <AnalysisTab holding={editedHolding} />
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex flex-shrink-0 flex-col justify-between gap-2 border-t p-3 sm:flex-row">
          <Button
            variant="outline"
            onClick={onCancel}
            className="h-9 w-full sm:w-auto"
            size="sm"
          >
            <X className="mr-2 h-3 w-3" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="h-9 w-full sm:w-auto"
            size="sm"
          >
            <Save className="mr-2 h-3 w-3" />
            {isNew ? "Create Holding" : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 基本信息标签页
function BasicInfoTab({
  holding,
  onUpdate,
}: {
  holding: InvestmentHolding;
  onUpdate: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      {/* Basic Information */}
      <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Label htmlFor="name" className="text-sm font-medium">
            Investment Name *
          </Label>
          <Input
            id="name"
            value={holding.name}
            onChange={(e) => onUpdate("name", e.target.value)}
            placeholder="e.g., Vanguard S&P 500 ETF"
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="symbol" className="text-sm font-medium">
            Symbol
          </Label>
          <Input
            id="symbol"
            value={holding.symbol || ""}
            onChange={(e) => onUpdate("symbol", e.target.value)}
            placeholder="e.g., VOO"
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="type" className="text-sm font-medium">
            Investment Type
          </Label>
          <Select
            value={holding.type}
            onValueChange={(value) => onUpdate("type", value)}
          >
            <SelectTrigger className="mt-1 h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="stock">Stock</SelectItem>
              <SelectItem value="etf">ETF</SelectItem>
              <SelectItem value="mutual_fund">Mutual Fund</SelectItem>
              <SelectItem value="bond">Bond</SelectItem>
              <SelectItem value="crypto">Cryptocurrency</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="currency" className="text-sm font-medium">
            Currency
          </Label>
          <Select
            value={holding.currency}
            onValueChange={(value) => onUpdate("currency", value)}
          >
            <SelectTrigger className="mt-1 h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
              <SelectItem value="JPY">JPY</SelectItem>
              <SelectItem value="CNY">CNY</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="current-price" className="text-sm font-medium">
            Current Price *
          </Label>
          <CurrencyInputField
            value={holding.currentPrice}
            onChange={(value) => onUpdate("currentPrice", value)}
            currency={holding.currency}
            placeholder="Enter current price"
            className="mt-1 h-9"
          />
        </div>
      </div>

      {/* Price Info */}
      <div className="bg-muted/20 rounded border p-3">
        <div className="text-muted-foreground flex items-center justify-between text-xs">
          <span>💡 Used for real-time returns calculation</span>
          {holding.lastUpdated && (
            <span>🕒 Updated: {holding.lastUpdated.toLocaleString()}</span>
          )}
        </div>
      </div>
    </div>
  );
}

// 投资记录标签页
function RecordsTab({
  holding,
  onAddRecord,
  onUpdateRecord,
  onDeleteRecord,
  editingRecord,
  setEditingRecord,
  isAddingRecord,
  setIsAddingRecord,
}: {
  holding: InvestmentHolding;
  onAddRecord: (record: Partial<InvestmentRecord>) => void;
  onUpdateRecord: (recordId: string, record: Partial<InvestmentRecord>) => void;
  onDeleteRecord: (recordId: string) => void;
  editingRecord: InvestmentRecord | null;
  setEditingRecord: (record: InvestmentRecord | null) => void;
  isAddingRecord: boolean;
  setIsAddingRecord: (adding: boolean) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">
          Investment Records ({holding.records.length})
        </h4>
        <Button size="sm" onClick={() => setIsAddingRecord(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Record
        </Button>
      </div>

      {/* Add/Edit Record Form */}
      {(isAddingRecord || editingRecord) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {isAddingRecord
                ? "Add Investment Record"
                : "Edit Investment Record"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <RecordForm
              initialData={editingRecord}
              currency={holding.currency}
              onSave={(data) => {
                if (isAddingRecord) {
                  onAddRecord(data);
                } else if (editingRecord) {
                  onUpdateRecord(editingRecord.id, data);
                }
              }}
              onCancel={() => {
                setIsAddingRecord(false);
                setEditingRecord(null);
              }}
            />
          </CardContent>
        </Card>
      )}

      {/* Records Display */}
      {holding.records.length === 0 ? (
        <div className="bg-muted/20 rounded-lg py-12 text-center">
          <Calendar className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <p className="text-muted-foreground text-lg font-medium">
            No investment records yet
          </p>
          <p className="text-muted-foreground mt-2 text-sm">
            Add your first investment record to get started
          </p>
        </div>
      ) : (
        <>
          {/* Desktop Table View */}
          <div className="hidden overflow-x-auto rounded-lg border lg:block">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Shares</TableHead>
                  <TableHead>Fees</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {holding.records.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      {new Date(record.date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          record.type === "buy"
                            ? "default"
                            : record.type === "sell"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {record.type.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(record.amount, holding.currency)}
                    </TableCell>
                    <TableCell>
                      {record.price
                        ? formatCurrency(record.price, holding.currency)
                        : "-"}
                    </TableCell>
                    <TableCell>
                      {record.shares ? record.shares.toFixed(4) : "-"}
                    </TableCell>
                    <TableCell>
                      {record.fees
                        ? formatCurrency(record.fees, holding.currency)
                        : "-"}
                    </TableCell>
                    <TableCell className="max-w-32 truncate">
                      {record.note || "-"}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingRecord(record)}
                        >
                          <Edit2 className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteRecord(record.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Mobile Card View */}
          <div className="space-y-4 lg:hidden">
            {holding.records.map((record) => (
              <Card key={record.id} className="p-4">
                <div className="mb-3 flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        record.type === "buy"
                          ? "default"
                          : record.type === "sell"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {record.type.toUpperCase()}
                    </Badge>
                    <span className="text-muted-foreground text-sm">
                      {new Date(record.date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingRecord(record)}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteRecord(record.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-muted-foreground">Amount:</span>
                    <p className="font-medium">
                      {formatCurrency(record.amount, holding.currency)}
                    </p>
                  </div>
                  {record.price && (
                    <div>
                      <span className="text-muted-foreground">Price:</span>
                      <p className="font-medium">
                        {formatCurrency(record.price, holding.currency)}
                      </p>
                    </div>
                  )}
                  {record.shares && (
                    <div>
                      <span className="text-muted-foreground">Shares:</span>
                      <p className="font-medium">{record.shares.toFixed(4)}</p>
                    </div>
                  )}
                  {record.fees && (
                    <div>
                      <span className="text-muted-foreground">Fees:</span>
                      <p className="font-medium">
                        {formatCurrency(record.fees, holding.currency)}
                      </p>
                    </div>
                  )}
                </div>

                {record.note && (
                  <div className="mt-3 border-t pt-3">
                    <span className="text-muted-foreground text-sm">Note:</span>
                    <p className="mt-1 text-sm">{record.note}</p>
                  </div>
                )}
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
}

// 分析标签页
function AnalysisTab({ holding }: { holding: InvestmentHolding }) {
  return (
    <div className="space-y-4">
      {/* Key Metrics */}
      <div className="grid grid-cols-2 gap-3 lg:grid-cols-4">
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-3 text-center">
            <DollarSign className="mx-auto mb-2 h-6 w-6 text-blue-600" />
            <p className="text-muted-foreground text-xs font-medium">
              Current Price
            </p>
            <p className="text-sm font-bold text-blue-700">
              {formatCurrency(holding.currentPrice, holding.currency)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-3 text-center">
            <Target className="mx-auto mb-2 h-6 w-6 text-green-600" />
            <p className="text-muted-foreground text-xs font-medium">
              Total Shares
            </p>
            <p className="text-sm font-bold text-green-700">
              {holding.totalShares.toFixed(3)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-3 text-center">
            <BarChart3 className="mx-auto mb-2 h-6 w-6 text-purple-600" />
            <p className="text-muted-foreground text-xs font-medium">
              Average Cost
            </p>
            <p className="text-sm font-bold text-purple-700">
              {formatCurrency(holding.averageCost, holding.currency)}
            </p>
          </CardContent>
        </Card>

        <Card
          className={`border-2 bg-gradient-to-br ${
            holding.unrealizedGainPercent >= 0
              ? "border-green-300 from-green-50 to-green-100"
              : "border-red-300 from-red-50 to-red-100"
          }`}
        >
          <CardContent className="p-3 text-center">
            <TrendingUp
              className={`mx-auto mb-2 h-6 w-6 ${
                holding.unrealizedGainPercent >= 0
                  ? "text-green-600"
                  : "text-red-600"
              }`}
            />
            <p className="text-muted-foreground text-xs font-medium">
              Return %
            </p>
            <p
              className={`text-sm font-bold ${
                holding.unrealizedGainPercent >= 0
                  ? "text-green-700"
                  : "text-red-700"
              }`}
            >
              {CurrencyUtils.formatPercentage(holding.unrealizedGainPercent)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Investment Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Invested</span>
              <span className="font-medium">
                {formatCurrency(holding.totalInvested, holding.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Current Value</span>
              <span className="font-medium">
                {formatCurrency(holding.currentValue, holding.currency)}
              </span>
            </div>
            <div className="flex justify-between border-t pt-3">
              <span className="text-muted-foreground">Unrealized P&L</span>
              <span
                className={`font-medium ${
                  holding.unrealizedGain >= 0
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {formatCurrency(holding.unrealizedGain, holding.currency)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Position Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Investment Type</span>
              <Badge variant="outline">
                {holding.type.replace("_", " ").toUpperCase()}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Currency</span>
              <span className="font-medium">{holding.currency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Records Count</span>
              <span className="font-medium">{holding.records.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Last Updated</span>
              <span className="text-muted-foreground text-sm">
                {holding.lastUpdated.toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      {holding.records.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {holding.unrealizedGainPercent > 20 && (
                <div className="flex items-center gap-2 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span className="text-sm">
                    Strong performance! Your investment is up{" "}
                    {CurrencyUtils.formatPercentage(
                      holding.unrealizedGainPercent,
                    )}
                  </span>
                </div>
              )}

              {holding.unrealizedGainPercent < -10 && (
                <div className="flex items-center gap-2 text-red-600">
                  <TrendingUp className="h-4 w-4 rotate-180" />
                  <span className="text-sm">
                    Consider reviewing your position. Currently down{" "}
                    {CurrencyUtils.formatPercentage(
                      Math.abs(holding.unrealizedGainPercent),
                    )}
                  </span>
                </div>
              )}

              {holding.currentPrice > holding.averageCost * 1.1 && (
                <div className="flex items-center gap-2 text-blue-600">
                  <Target className="h-4 w-4" />
                  <span className="text-sm">
                    Current price is{" "}
                    {(
                      (holding.currentPrice / holding.averageCost - 1) *
                      100
                    ).toFixed(1)}
                    % above your average cost
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// 投资记录表单组件
function RecordForm({
  initialData,
  currency,
  onSave,
  onCancel,
}: {
  initialData?: InvestmentRecord | null;
  currency: string;
  onSave: (data: Partial<InvestmentRecord>) => void;
  onCancel: () => void;
}) {
  const [date, setDate] = useState(
    initialData?.date
      ? new Date(initialData.date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
  );
  const [type, setType] = useState<"buy" | "sell" | "dividend">(
    initialData?.type || "buy",
  );
  const [amount, setAmount] = useState(initialData?.amount || 0);
  const [price, setPrice] = useState(initialData?.price || 0);
  const [shares, setShares] = useState(initialData?.shares || 0);
  const [fees, setFees] = useState(initialData?.fees || 0);
  const [note, setNote] = useState(initialData?.note || "");

  const handleSave = () => {
    onSave({
      date: new Date(date),
      type,
      amount,
      price: price || undefined,
      shares: shares || undefined,
      fees: fees || undefined,
      note: note.trim() || undefined,
    });
  };

  return (
    <div className="space-y-4">
      {/* Transaction Info */}
      <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
        <div>
          <Label htmlFor="date" className="text-sm font-medium">
            Date
          </Label>
          <Input
            id="date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="type" className="text-sm font-medium">
            Type
          </Label>
          <Select value={type} onValueChange={(value: any) => setType(value)}>
            <SelectTrigger className="mt-1 h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="buy">🟢 Buy</SelectItem>
              <SelectItem value="sell">🔴 Sell</SelectItem>
              <SelectItem value="dividend">💰 Dividend</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="amount" className="text-sm font-medium">
            Amount *
          </Label>
          <CurrencyInputField
            value={amount}
            onChange={setAmount}
            currency={currency}
            placeholder="Total amount"
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="price" className="text-sm font-medium">
            Price/Share
          </Label>
          <CurrencyInputField
            value={price}
            onChange={setPrice}
            currency={currency}
            placeholder="Price per share"
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="shares" className="text-sm font-medium">
            Shares
          </Label>
          <Input
            id="shares"
            type="number"
            step="0.0001"
            value={shares}
            onChange={(e) => setShares(parseFloat(e.target.value) || 0)}
            placeholder="Number of shares"
            className="mt-1 h-9"
          />
        </div>

        <div>
          <Label htmlFor="fees" className="text-sm font-medium">
            Fees
          </Label>
          <CurrencyInputField
            value={fees}
            onChange={setFees}
            currency={currency}
            placeholder="Transaction fees"
            className="mt-1 h-9"
          />
        </div>

        <div className="sm:col-span-2">
          <Label htmlFor="note" className="text-sm font-medium">
            Note (Optional)
          </Label>
          <Input
            id="note"
            value={note}
            onChange={(e) => setNote(e.target.value)}
            placeholder="e.g., Monthly DCA, Rebalancing"
            className="mt-1 h-9"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col justify-end gap-2 border-t pt-3 sm:flex-row">
        <Button
          variant="outline"
          onClick={onCancel}
          className="h-9 w-full sm:w-auto"
          size="sm"
        >
          Cancel
        </Button>
        <Button onClick={handleSave} className="h-9 w-full sm:w-auto" size="sm">
          Save Record
        </Button>
      </div>
    </div>
  );
}
