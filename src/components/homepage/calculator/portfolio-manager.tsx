"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Calculator,
  TrendingUp,
  DollarSign,
  PieChart,
  LineChart,
  Plus,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  Sparkles,
  ArrowRight,
  Settings,
  BarChart3,
  Calendar,
  Target,
} from "lucide-react";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Cell,
  ResponsiveContainer,
  Line<PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import type {
  InvestmentPortfolio,
  AssetAllocation,
  InvestmentRecord,
  SupportedCurrency,
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
} from "@/lib/calculations/types";
import { InvestmentCalculationEngine } from "@/lib/calculations/engines";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import {
  PortfolioEditForm,
  AssetForm,
  OverviewView,
  AssetsView,
  TimelineView,
} from "./portfolio-components";

// 预定义颜色
const ASSET_COLORS = [
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#ec4899",
  "#6366f1",
];

// 免费版限制
const FREE_TIER_LIMITS = {
  maxAssets: 3,
  maxPortfolios: 1,
};

export function PortfolioManager() {
  // 全局设置
  const [displayCurrency, setDisplayCurrency] =
    useState<SupportedCurrency>("USD");
  const [viewMode, setViewMode] = useState<"overview" | "assets" | "timeline">(
    "overview",
  );

  // 投资组合管理
  const [portfolios, setPortfolios] = useState<InvestmentPortfolio[]>([
    {
      id: "1",
      name: "My Investment Portfolio",
      description: "Diversified investment strategy",
      displayCurrency: "USD",
      assets: [
        {
          id: "1",
          name: "US Stock Market",
          percentage: 60,
          color: ASSET_COLORS[0],
          investmentCurrency: "USD",
          amount: 50000,
          calculationType: "lump_sum",
          params: {
            principal: 50000,
            annualRate: 8,
            years: 10,
            currency: "USD",
            name: "US Stock Market",
          },
        },
        {
          id: "2",
          name: "Japanese Bonds",
          percentage: 25,
          color: ASSET_COLORS[1],
          investmentCurrency: "JPY",
          amount: 2500000, // 25000 USD equivalent
          calculationType: "dca",
          params: {
            initialInvestment: 1000000,
            monthlyContribution: 125000,
            annualRate: 4,
            years: 10,
            currency: "JPY",
            name: "Japanese Bonds",
          },
        },
        {
          id: "3",
          name: "European Real Estate",
          percentage: 15,
          color: ASSET_COLORS[2],
          investmentCurrency: "EUR",
          amount: 12500, // 15000 USD equivalent
          calculationType: "lump_sum",
          params: {
            principal: 12500,
            annualRate: 6,
            years: 10,
            currency: "EUR",
            name: "European Real Estate",
          },
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
    },
  ]);

  const [activePortfolio, setActivePortfolio] = useState<string>("1");
  const [isAddAssetOpen, setIsAddAssetOpen] = useState(false);
  const [isEditPortfolioOpen, setIsEditPortfolioOpen] = useState(false);
  const [editingAsset, setEditingAsset] = useState<AssetAllocation | null>(
    null,
  );

  // 获取当前组合
  const currentPortfolio = portfolios.find((p) => p.id === activePortfolio);

  // 计算所有资产的结果
  useEffect(() => {
    if (!currentPortfolio) return;

    const updatedAssets = currentPortfolio.assets.map((asset) => {
      try {
        let result;
        switch (asset.calculationType) {
          case "lump_sum":
            result = InvestmentCalculationEngine.calculateLumpSum(
              asset.params as LumpSumParams,
            );
            break;
          case "dca":
            result = InvestmentCalculationEngine.calculateDCA(
              asset.params as DCAParams,
            );
            break;
          case "retirement":
            result = InvestmentCalculationEngine.calculateRetirement(
              asset.params as RetirementParams,
            );
            break;
          case "savings_goal":
            result = InvestmentCalculationEngine.calculateSavingsGoal(
              asset.params as SavingsGoalParams,
            );
            break;
          default:
            result = null;
        }
        return { ...asset, result };
      } catch (error) {
        console.error("Calculation error for asset:", asset.name, error);
        return asset;
      }
    });

    // 计算组合总值
    const totalValue = updatedAssets.reduce((sum, asset) => {
      if (asset.result) {
        // 这里应该考虑汇率转换，暂时简化处理
        return sum + asset.result.finalAmount;
      }
      return sum;
    }, 0);

    const totalGains = updatedAssets.reduce((sum, asset) => {
      if (asset.result) {
        return sum + asset.result.totalGains;
      }
      return sum;
    }, 0);

    const totalContributions = updatedAssets.reduce((sum, asset) => {
      if (asset.result) {
        return sum + asset.result.totalContributions;
      }
      return sum;
    }, 0);

    const totalReturn =
      totalContributions > 0 ? (totalGains / totalContributions) * 100 : 0;

    // 更新组合
    setPortfolios((prev) =>
      prev.map((p) =>
        p.id === activePortfolio
          ? {
              ...p,
              assets: updatedAssets,
              totalValue,
              totalGains,
              totalReturn,
            }
          : p,
      ),
    );
  }, [
    activePortfolio,
    currentPortfolio?.assets.map((a) => JSON.stringify(a.params)).join(","),
  ]);

  // 添加资产
  const addAsset = (assetData: Partial<AssetAllocation>) => {
    if (!currentPortfolio) return;

    // 免费版限制检查
    if (currentPortfolio.assets.length >= FREE_TIER_LIMITS.maxAssets) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxAssets} assets. Upgrade for unlimited assets.`,
      );
      return;
    }

    const newAsset: AssetAllocation = {
      id: Date.now().toString(),
      name: assetData.name || "New Asset",
      percentage: assetData.percentage || 0,
      color: ASSET_COLORS[currentPortfolio.assets.length % ASSET_COLORS.length],
      investmentCurrency: assetData.investmentCurrency || displayCurrency,
      amount: assetData.amount || 0,
      calculationType: assetData.calculationType || "lump_sum",
      params: assetData.params || {
        principal: assetData.amount || 10000,
        annualRate: 7,
        years: 10,
        currency: assetData.investmentCurrency || displayCurrency,
        name: assetData.name || "New Asset",
      },
    };

    setPortfolios((prev) =>
      prev.map((p) =>
        p.id === activePortfolio
          ? { ...p, assets: [...p.assets, newAsset] }
          : p,
      ),
    );
    setIsAddAssetOpen(false);
  };

  // 删除资产
  const removeAsset = (assetId: string) => {
    if (!currentPortfolio) return;

    setPortfolios((prev) =>
      prev.map((p) =>
        p.id === activePortfolio
          ? { ...p, assets: p.assets.filter((a) => a.id !== assetId) }
          : p,
      ),
    );
  };

  // 更新资产
  const updateAsset = (assetId: string, updates: Partial<AssetAllocation>) => {
    if (!currentPortfolio) return;

    setPortfolios((prev) =>
      prev.map((p) =>
        p.id === activePortfolio
          ? {
              ...p,
              assets: p.assets.map((a) =>
                a.id === assetId ? { ...a, ...updates } : a,
              ),
            }
          : p,
      ),
    );
  };

  // 更新资产的投资记录
  const updateAssetRecords = (assetId: string, records: InvestmentRecord[]) => {
    updateAsset(assetId, { records });
  };

  // 准备饼图数据
  const pieChartData =
    currentPortfolio?.assets.map((asset) => ({
      name: asset.name,
      value: asset.result?.finalAmount || asset.amount,
      percentage: asset.percentage,
      color: asset.color,
      currency: asset.investmentCurrency,
    })) || [];

  // 准备时间线数据（模拟数据，实际应该基于投资记录）
  const timelineData = Array.from({ length: 121 }, (_, i) => {
    const month = i;
    const year = Math.floor(month / 12);
    const data: any = { month, year: `Year ${year}` };

    currentPortfolio?.assets.forEach((asset) => {
      if (asset.result && asset.calculationType === "dca") {
        // 模拟DCA增长曲线
        const monthlyGrowth = Math.pow(
          1 + (asset.params as DCAParams).annualRate / 100 / 12,
          month,
        );
        const monthlyContribution = (asset.params as DCAParams)
          .monthlyContribution;
        const initialInvestment =
          (asset.params as DCAParams).initialInvestment || 0;

        data[asset.name] =
          initialInvestment * monthlyGrowth +
          monthlyContribution *
            ((monthlyGrowth - 1) /
              ((asset.params as DCAParams).annualRate / 100 / 12));
      } else if (asset.result) {
        // 模拟一次性投资增长曲线
        const monthlyGrowth = Math.pow(
          1 + (asset.params as LumpSumParams).annualRate / 100 / 12,
          month,
        );
        data[asset.name] =
          (asset.params as LumpSumParams).principal * monthlyGrowth;
      }
    });

    return data;
  });

  return (
    <section id="portfolio-calculator" className="bg-background py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto mb-16 max-w-2xl text-center">
          <Badge variant="secondary" className="mb-4">
            <PieChart className="mr-2 h-3 w-3" />
            Professional Portfolio Manager
          </Badge>

          <h2 className="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
            Build & Analyze Investment Portfolios
          </h2>

          <p className="text-muted-foreground mt-4 text-lg">
            Create diversified portfolios with multi-currency assets, visualize
            growth with beautiful charts, and track your investment timeline.
            Free tier includes up to 3 assets.
          </p>
        </div>

        {/* Main Interface */}
        <div className="mx-auto max-w-6xl">
          {/* Controls */}
          <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-4">
              <Select
                value={displayCurrency}
                onValueChange={(value: SupportedCurrency) =>
                  setDisplayCurrency(value)
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="JPY">JPY (¥)</SelectItem>
                  <SelectItem value="CNY">CNY (¥)</SelectItem>
                  <SelectItem value="CAD">CAD (C$)</SelectItem>
                  <SelectItem value="AUD">AUD (A$)</SelectItem>
                </SelectContent>
              </Select>

              <Tabs
                value={viewMode}
                onValueChange={(value: "overview" | "assets" | "timeline") =>
                  setViewMode(value)
                }
              >
                <TabsList>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="assets">Assets</TabsTrigger>
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <div className="flex items-center gap-2">
              <Dialog
                open={isEditPortfolioOpen}
                onOpenChange={setIsEditPortfolioOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Edit2 className="mr-2 h-4 w-4" />
                    Edit Portfolio
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Edit Portfolio</DialogTitle>
                    <DialogDescription>
                      Customize your portfolio name and description.
                    </DialogDescription>
                  </DialogHeader>
                  <PortfolioEditForm
                    portfolio={currentPortfolio}
                    onSave={(updates) => {
                      if (currentPortfolio) {
                        setPortfolios((prev) =>
                          prev.map((p) =>
                            p.id === activePortfolio ? { ...p, ...updates } : p,
                          ),
                        );
                      }
                      setIsEditPortfolioOpen(false);
                    }}
                  />
                </DialogContent>
              </Dialog>

              <Dialog open={isAddAssetOpen} onOpenChange={setIsAddAssetOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Asset
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Asset</DialogTitle>
                    <DialogDescription>
                      Add a new investment asset to your portfolio.
                      {currentPortfolio &&
                        currentPortfolio.assets.length >=
                          FREE_TIER_LIMITS.maxAssets && (
                          <span className="mt-2 block text-amber-600">
                            ⚠️ Free tier limit reached. Upgrade for unlimited
                            assets.
                          </span>
                        )}
                    </DialogDescription>
                  </DialogHeader>
                  <AssetForm
                    onSave={addAsset}
                    displayCurrency={displayCurrency}
                    isDisabled={
                      currentPortfolio
                        ? currentPortfolio.assets.length >=
                          FREE_TIER_LIMITS.maxAssets
                        : false
                    }
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Content based on view mode */}
          {viewMode === "overview" && (
            <OverviewView
              portfolio={currentPortfolio}
              pieChartData={pieChartData}
              displayCurrency={displayCurrency}
            />
          )}

          {viewMode === "assets" && (
            <AssetsView
              portfolio={currentPortfolio}
              displayCurrency={displayCurrency}
              onUpdateAsset={updateAsset}
              onRemoveAsset={removeAsset}
              onEditAsset={setEditingAsset}
            />
          )}

          {viewMode === "timeline" && (
            <TimelineView
              portfolio={currentPortfolio}
              timelineData={timelineData}
              displayCurrency={displayCurrency}
              onUpdateAssetRecords={updateAssetRecords}
            />
          )}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-primary/5 border-primary/20 mx-auto max-w-2xl rounded-2xl border p-8">
            <Sparkles className="text-primary mx-auto mb-4 h-12 w-12" />
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Unlock Advanced Portfolio Features
            </h3>
            <p className="text-muted-foreground mb-6">
              Unlimited assets, cloud sync, professional PDF reports, advanced
              analytics, real-time market data integration, and portfolio
              rebalancing recommendations.
            </p>
            <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/signup">
                  Start Pro Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/dashboard">View Full Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
