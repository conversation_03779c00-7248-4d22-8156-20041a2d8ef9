"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import {
  CurrencyInputField,
  PercentageInputField,
} from "@/components/calculator/core/InputField";
import { formatCurrency } from "@/lib/utils/currency";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 类型定义
interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: SupportedCurrency;
}

interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  investmentMethod: "lump_sum" | "dca" | "mixed";
  currency: SupportedCurrency;
  targetAllocation: number;
}

interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

// 简化的Portfolio Form - 支持直接管理Holdings
export function PortfolioForm({
  onSave,
  initialData,
  availableHoldings = [],
}: {
  onSave: (data: Partial<Portfolio>) => void;
  initialData?: Portfolio;
  availableHoldings?: any[];
}) {
  const [name, setName] = useState(initialData?.name || "");
  const [description, setDescription] = useState(
    initialData?.description || "",
  );
  const [strategy, setStrategy] = useState(initialData?.strategy || "");
  const [riskLevel, setRiskLevel] = useState<
    "conservative" | "moderate" | "aggressive"
  >(initialData?.riskLevel || "moderate");
  const [currency, setCurrency] = useState<SupportedCurrency>(
    initialData?.currency || "USD",
  );
  const [selectedHoldings, setSelectedHoldings] = useState<
    { holdingId: string; targetAllocation: number; isVisible: boolean }[]
  >(initialData?.holdings || []);

  const handleSave = () => {
    if (!name.trim()) {
      alert("Please enter a portfolio name");
      return;
    }

    onSave({
      name,
      description,
      strategy,
      riskLevel,
      currency,
      holdings: selectedHoldings,
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="portfolio-name">Portfolio Name</Label>
          <Input
            id="portfolio-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="My Investment Portfolio"
          />
        </div>
        <div>
          <Label htmlFor="portfolio-currency">Base Currency</Label>
          <Select
            value={currency}
            onValueChange={(value: SupportedCurrency) => setCurrency(value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD ($)</SelectItem>
              <SelectItem value="EUR">EUR (€)</SelectItem>
              <SelectItem value="GBP">GBP (£)</SelectItem>
              <SelectItem value="JPY">JPY (¥)</SelectItem>
              <SelectItem value="CNY">CNY (¥)</SelectItem>
              <SelectItem value="CAD">CAD (C$)</SelectItem>
              <SelectItem value="AUD">AUD (A$)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="portfolio-description">Description (Optional)</Label>
        <Input
          id="portfolio-description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your investment strategy..."
        />
      </div>

      <div>
        <Label htmlFor="portfolio-strategy">
          Investment Strategy (Optional)
        </Label>
        <Input
          id="portfolio-strategy"
          value={strategy}
          onChange={(e) => setStrategy(e.target.value)}
          placeholder="e.g., 60% stocks + 40% bonds"
        />
      </div>

      <div>
        <Label htmlFor="risk-level">Risk Level</Label>
        <Select
          value={riskLevel}
          onValueChange={(value: any) => setRiskLevel(value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="conservative">Conservative</SelectItem>
            <SelectItem value="moderate">Moderate</SelectItem>
            <SelectItem value="aggressive">Aggressive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Holdings Selection */}
      {availableHoldings.length > 0 && (
        <div className="space-y-4">
          <Label>Select Holdings for Portfolio</Label>
          <div className="max-h-60 space-y-3 overflow-y-auto">
            {availableHoldings.map((holding) => {
              const isSelected = selectedHoldings.some(
                (h) => h.holdingId === holding.id,
              );
              const config = selectedHoldings.find(
                (h) => h.holdingId === holding.id,
              );

              return (
                <div
                  key={holding.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedHoldings([
                            ...selectedHoldings,
                            {
                              holdingId: holding.id,
                              targetAllocation: 0,
                              isVisible: true,
                            },
                          ]);
                        } else {
                          setSelectedHoldings(
                            selectedHoldings.filter(
                              (h) => h.holdingId !== holding.id,
                            ),
                          );
                        }
                      }}
                    />
                    <div
                      className="h-3 w-3 rounded-full"
                      style={{ backgroundColor: holding.color }}
                    />
                    <div>
                      <p className="font-medium">{holding.name}</p>
                      <p className="text-muted-foreground text-sm">
                        {holding.symbol} •{" "}
                        {holding.type?.replace("_", " ").toUpperCase()}
                      </p>
                    </div>
                  </div>

                  {isSelected && (
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="0"
                        value={config?.targetAllocation || 0}
                        onChange={(e) => {
                          const allocation = parseFloat(e.target.value) || 0;
                          setSelectedHoldings(
                            selectedHoldings.map((h) =>
                              h.holdingId === holding.id
                                ? { ...h, targetAllocation: allocation }
                                : h,
                            ),
                          );
                        }}
                        className="w-20"
                        min="0"
                        max="100"
                      />
                      <span className="text-muted-foreground text-sm">%</span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {selectedHoldings.length > 0 && (
            <div className="text-muted-foreground text-sm">
              Total allocation:{" "}
              {selectedHoldings
                .reduce((sum, h) => sum + h.targetAllocation, 0)
                .toFixed(1)}
              %
            </div>
          )}
        </div>
      )}

      <DialogFooter>
        <Button onClick={handleSave}>
          {initialData ? "Update Portfolio" : "Create Portfolio"}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Holding Form
export function HoldingForm({
  onSave,
  initialData,
  currency,
  isDisabled = false,
}: {
  onSave: (data: Partial<InvestmentHolding>) => void;
  initialData?: InvestmentHolding;
  currency: SupportedCurrency;
  isDisabled?: boolean;
}) {
  const [name, setName] = useState(initialData?.name || "");
  const [symbol, setSymbol] = useState(initialData?.symbol || "");
  const [type, setType] = useState<
    "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other"
  >(initialData?.type || "etf");
  const [investmentMethod, setInvestmentMethod] = useState<
    "lump_sum" | "dca" | "mixed"
  >(initialData?.investmentMethod || "dca");
  const [holdingCurrency, setHoldingCurrency] = useState<SupportedCurrency>(
    initialData?.currency || currency,
  );
  const [currentPrice, setCurrentPrice] = useState(
    initialData?.currentPrice || 0,
  );
  const [targetAllocation, setTargetAllocation] = useState(
    initialData?.targetAllocation || 0,
  );

  const handleSave = () => {
    if (!name.trim()) {
      alert("Please enter a holding name");
      return;
    }

    onSave({
      name,
      symbol: symbol.trim() || undefined,
      type,
      investmentMethod,
      currency: holdingCurrency,
      currentPrice,
      targetAllocation,
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="holding-name">Investment Name</Label>
          <Input
            id="holding-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Vanguard S&P 500 ETF"
          />
        </div>
        <div>
          <Label htmlFor="holding-symbol">Symbol (Optional)</Label>
          <Input
            id="holding-symbol"
            value={symbol}
            onChange={(e) => setSymbol(e.target.value.toUpperCase())}
            placeholder="VOO"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="holding-type">Investment Type</Label>
          <Select value={type} onValueChange={(value: any) => setType(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="etf">ETF</SelectItem>
              <SelectItem value="mutual_fund">Mutual Fund</SelectItem>
              <SelectItem value="stock">Individual Stock</SelectItem>
              <SelectItem value="bond">Bond</SelectItem>
              <SelectItem value="crypto">Cryptocurrency</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="investment-method">Investment Method</Label>
          <Select
            value={investmentMethod}
            onValueChange={(value: any) => setInvestmentMethod(value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dca">Dollar Cost Averaging</SelectItem>
              <SelectItem value="lump_sum">Lump Sum</SelectItem>
              <SelectItem value="mixed">Mixed Strategy</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="holding-currency">Currency</Label>
          <Select
            value={holdingCurrency}
            onValueChange={(value: SupportedCurrency) =>
              setHoldingCurrency(value)
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD ($)</SelectItem>
              <SelectItem value="EUR">EUR (€)</SelectItem>
              <SelectItem value="GBP">GBP (£)</SelectItem>
              <SelectItem value="JPY">JPY (¥)</SelectItem>
              <SelectItem value="CNY">CNY (¥)</SelectItem>
              <SelectItem value="CAD">CAD (C$)</SelectItem>
              <SelectItem value="AUD">AUD (A$)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <PercentageInputField
            label="Target Allocation"
            value={targetAllocation}
            onChange={setTargetAllocation}
            min={0}
            max={100}
            showSlider
          />
        </div>
      </div>

      {/* Current Price */}
      <div>
        <Label htmlFor="current-price">Current Market Price</Label>
        <CurrencyInputField
          value={currentPrice}
          onChange={setCurrentPrice}
          currency={holdingCurrency}
          placeholder="Enter current market price"
        />
        <p className="text-muted-foreground mt-1 text-sm">
          This is used to calculate real-time returns and current value
        </p>
      </div>

      {isDisabled && (
        <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
          <p className="text-sm text-amber-800">
            ⚠️ Free tier limit reached. Upgrade to add more holdings.
          </p>
        </div>
      )}

      <DialogFooter>
        <Button onClick={handleSave} disabled={isDisabled}>
          {isDisabled
            ? "Upgrade Required"
            : initialData
              ? "Update Holding"
              : "Add Holding"}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Investment Record Form
export function RecordForm({
  onSave,
  initialData,
  currency,
}: {
  onSave: (data: Partial<InvestmentRecord>) => void;
  initialData?: InvestmentRecord;
  currency: SupportedCurrency;
}) {
  const [date, setDate] = useState(
    initialData?.date
      ? new Date(initialData.date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
  );
  const [type, setType] = useState<"buy" | "sell" | "dividend">(
    initialData?.type || "buy",
  );
  const [amount, setAmount] = useState(initialData?.amount || 0);
  const [price, setPrice] = useState(initialData?.price || 0);
  const [shares, setShares] = useState(initialData?.shares || 0);
  const [fees, setFees] = useState(initialData?.fees || 0);
  const [note, setNote] = useState(initialData?.note || "");

  // Auto-calculate shares when amount and price change
  React.useEffect(() => {
    if (amount > 0 && price > 0 && type === "buy") {
      setShares(amount / price);
    }
  }, [amount, price, type]);

  const handleSave = () => {
    if (amount <= 0) {
      alert("Please enter a valid amount");
      return;
    }

    onSave({
      date: new Date(date),
      type,
      amount,
      price: price > 0 ? price : undefined,
      shares: shares > 0 ? shares : undefined,
      fees: fees > 0 ? fees : undefined,
      note: note.trim() || undefined,
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="record-date">Date</Label>
          <Input
            id="record-date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="record-type">Transaction Type</Label>
          <Select value={type} onValueChange={(value: any) => setType(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="buy">Buy</SelectItem>
              <SelectItem value="sell">Sell</SelectItem>
              <SelectItem value="dividend">Dividend</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <CurrencyInputField
        label="Amount"
        value={amount}
        onChange={setAmount}
        currency={currency}
        min={0.01}
        placeholder="1,000"
      />

      <div className="grid grid-cols-2 gap-4">
        <CurrencyInputField
          label="Price per Share (Optional)"
          value={price}
          onChange={setPrice}
          currency={currency}
          min={0}
          placeholder="100.50"
        />
        <div>
          <Label htmlFor="record-shares">Shares</Label>
          <Input
            id="record-shares"
            type="number"
            value={shares}
            onChange={(e) => setShares(parseFloat(e.target.value) || 0)}
            placeholder="10.5"
            step="0.001"
          />
        </div>
      </div>

      <CurrencyInputField
        label="Fees (Optional)"
        value={fees}
        onChange={setFees}
        currency={currency}
        min={0}
        placeholder="5.00"
      />

      <div>
        <Label htmlFor="record-note">Note (Optional)</Label>
        <Input
          id="record-note"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Monthly investment, rebalancing, etc."
        />
      </div>

      {price > 0 && amount > 0 && (
        <div className="bg-muted/50 rounded-lg p-3">
          <p className="text-muted-foreground text-sm">
            Calculated shares:{" "}
            <span className="font-medium">{(amount / price).toFixed(4)}</span>
          </p>
          {fees > 0 && (
            <p className="text-muted-foreground text-sm">
              Total cost:{" "}
              <span className="font-medium">
                {formatCurrency(amount + fees, currency)}
              </span>
            </p>
          )}
        </div>
      )}

      <DialogFooter>
        <Button onClick={handleSave}>
          {initialData ? "Update Record" : "Add Record"}
        </Button>
      </DialogFooter>
    </div>
  );
}
