@import "tailwindcss";
@import "tw-animate-css";
@import "./markdoc.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: Oxanium, sans-serif;
  --font-mono: Fira Code, monospace;
  --font-serif: Merriweather, serif;
  --radius: 0.3rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.3rem;
  /* Dull Lavender Light Theme - Based on your color scheme */
  --card: oklch(0.98 0.015 280); /* Very light lavender background */
  --card-foreground: oklch(0.25 0.05 280); /* Dark lavender text */
  --popover: oklch(0.98 0.015 280);
  --popover-foreground: oklch(0.25 0.05 280);
  --primary: oklch(0.55 0.15 280); /* dull-lavender-600 equivalent */
  --primary-foreground: oklch(1 0 0); /* Pure white */
  --secondary: oklch(0.85 0.08 280); /* Light lavender secondary */
  --secondary-foreground: oklch(0.35 0.06 280);
  --muted: oklch(0.94 0.025 280); /* Subtle lavender muted */
  --muted-foreground: oklch(0.55 0.03 280);
  --accent: oklch(0.75 0.12 280); /* dull-lavender-400 equivalent */
  --accent-foreground: oklch(0.15 0.08 280);
  --destructive: oklch(0.55 0.15 15); /* Red for destructive actions */
  --border: oklch(0.88 0.04 280); /* Light lavender border */
  --input: oklch(0.88 0.04 280);
  --ring: oklch(0.55 0.15 280); /* Primary color for focus rings */
  --chart-1: oklch(0.55 0.15 280); /* Primary lavender */
  --chart-2: oklch(0.65 0.12 300); /* Slightly different hue */
  --chart-3: oklch(0.45 0.18 260); /* Deeper purple */
  --chart-4: oklch(0.75 0.1 320); /* Lighter purple-pink */
  --chart-5: oklch(0.35 0.2 280); /* Dark lavender */
  --sidebar: oklch(0.94 0.025 280);
  --sidebar-foreground: oklch(0.25 0.05 280);
  --sidebar-primary: oklch(0.55 0.15 280);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.75 0.12 280);
  --sidebar-accent-foreground: oklch(0.15 0.08 280);
  --sidebar-border: oklch(0.88 0.04 280);
  --sidebar-ring: oklch(0.55 0.15 280);
  --destructive-foreground: oklch(1 0 0);
  --font-sans: Oxanium, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Fira Code, monospace;
  --shadow-color: hsl(280 25% 20%); /* Lavender-tinted shadows */
  --shadow-opacity: 0.12;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 2px 3px 0px hsl(280 25% 20% / 0.06);
  --shadow-xs: 0px 2px 3px 0px hsl(280 25% 20% / 0.06);
  --shadow-sm:
    0px 2px 3px 0px hsl(280 25% 20% / 0.12),
    0px 1px 2px -1px hsl(280 25% 20% / 0.12);
  --shadow:
    0px 2px 3px 0px hsl(280 25% 20% / 0.12),
    0px 1px 2px -1px hsl(280 25% 20% / 0.12);
  --shadow-md:
    0px 2px 3px 0px hsl(280 25% 20% / 0.12),
    0px 2px 4px -1px hsl(280 25% 20% / 0.12);
  --shadow-lg:
    0px 2px 3px 0px hsl(280 25% 20% / 0.12),
    0px 4px 6px -1px hsl(280 25% 20% / 0.12);
  --shadow-xl:
    0px 2px 3px 0px hsl(280 25% 20% / 0.12),
    0px 8px 10px -1px hsl(280 25% 20% / 0.12);
  --shadow-2xl: 0px 2px 3px 0px hsl(280 25% 20% / 0.25);
  --tracking-normal: 0em;
  --background: oklch(0.99 0.008 280); /* Very light lavender background */
  --foreground: oklch(0.25 0.05 280); /* Dark lavender text */
}

.dark {
  /* Dull Lavender Dark Theme - Sophisticated dark mode */
  --background: oklch(
    0.12 0.03 280
  ); /* Deep lavender background (dull-lavender-950) */
  --foreground: oklch(0.95 0.015 280); /* Light lavender text */
  --card: oklch(
    0.18 0.04 280
  ); /* Slightly lighter dark lavender (dull-lavender-900) */
  --card-foreground: oklch(0.95 0.015 280);
  --popover: oklch(0.18 0.04 280);
  --popover-foreground: oklch(0.95 0.015 280);
  --primary: oklch(
    0.75 0.18 280
  ); /* Bright lavender for primary (dull-lavender-400) */
  --primary-foreground: oklch(0.12 0.03 280); /* Dark background for contrast */
  --secondary: oklch(
    0.35 0.08 280
  ); /* Medium dark lavender (dull-lavender-800) */
  --secondary-foreground: oklch(0.85 0.05 280); /* Light lavender text */
  --muted: oklch(0.22 0.05 280); /* Muted dark lavender */
  --muted-foreground: oklch(0.65 0.08 280); /* Medium lavender text */
  --accent: oklch(0.65 0.15 280); /* Accent lavender (dull-lavender-500) */
  --accent-foreground: oklch(0.15 0.04 280); /* Dark text for accent */
  --destructive: oklch(0.65 0.18 15); /* Bright red for destructive actions */
  --border: oklch(0.28 0.06 280); /* Border lavender */
  --input: oklch(0.28 0.06 280);
  --ring: oklch(0.75 0.18 280); /* Primary color for focus rings */
  --chart-1: oklch(0.75 0.18 280); /* Primary lavender */
  --chart-2: oklch(0.7 0.15 300); /* Purple variant */
  --chart-3: oklch(0.6 0.2 260); /* Deep purple */
  --chart-4: oklch(0.8 0.12 320); /* Light purple-pink */
  --chart-5: oklch(0.45 0.25 280); /* Dark intense lavender */
  --sidebar: oklch(0.15 0.035 280); /* Slightly lighter than background */
  --sidebar-foreground: oklch(0.95 0.015 280);
  --sidebar-primary: oklch(0.75 0.18 280);
  --sidebar-primary-foreground: oklch(0.12 0.03 280);
  --sidebar-accent: oklch(0.65 0.15 280);
  --sidebar-accent-foreground: oklch(0.15 0.04 280);
  --sidebar-border: oklch(0.28 0.06 280);
  --sidebar-ring: oklch(0.75 0.18 280);
  --destructive-foreground: oklch(1 0 0);
  --radius: 0.3rem;
  --font-sans: Oxanium, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Fira Code, monospace;
  --shadow-color: hsl(280 40% 8%); /* Deep lavender shadows */
  --shadow-opacity: 0.25;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 2px 3px 0px hsl(280 40% 8% / 0.15);
  --shadow-xs: 0px 2px 3px 0px hsl(280 40% 8% / 0.15);
  --shadow-sm:
    0px 2px 3px 0px hsl(280 40% 8% / 0.25),
    0px 1px 2px -1px hsl(280 40% 8% / 0.25);
  --shadow:
    0px 2px 3px 0px hsl(280 40% 8% / 0.25),
    0px 1px 2px -1px hsl(280 40% 8% / 0.25);
  --shadow-md:
    0px 2px 3px 0px hsl(280 40% 8% / 0.25),
    0px 2px 4px -1px hsl(280 40% 8% / 0.25);
  --shadow-lg:
    0px 2px 3px 0px hsl(280 40% 8% / 0.25),
    0px 4px 6px -1px hsl(280 40% 8% / 0.25);
  --shadow-xl:
    0px 2px 3px 0px hsl(280 40% 8% / 0.25),
    0px 8px 10px -1px hsl(280 40% 8% / 0.25);
  --shadow-2xl: 0px 2px 3px 0px hsl(280 40% 8% / 0.45);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}
