import {
  BaseCalculationResult,
  LumpSumResult,
  DCAResult,
  RetirementResult,
  SavingsGoalResult,
  YearlyBreakdown,
  MonthlyBreakdown,
  ChartDataPoint,
  SupportedCurrency,
  CurrencyInfo,
} from "./types";

// 格式化结果类型
interface FormattedResult {
  [key: string]: unknown;
}

// 货币信息配置
const currencyInfo: Record<SupportedCurrency, CurrencyInfo> = {
  USD: { code: "USD", symbol: "$", name: "US Dollar", locale: "en-US" },
  EUR: { code: "EUR", symbol: "€", name: "Euro", locale: "de-DE" },
  GBP: { code: "GBP", symbol: "£", name: "British Pound", locale: "en-GB" },
  JPY: { code: "JPY", symbol: "¥", name: "Japanese Yen", locale: "ja-JP" },
  CNY: { code: "CNY", symbol: "¥", name: "Chinese Yuan", locale: "zh-CN" },
  KRW: { code: "KRW", symbol: "₩", name: "Korean Won", locale: "ko-KR" },
  CAD: { code: "CAD", symbol: "C$", name: "Canadian Dollar", locale: "en-CA" },
  AUD: {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    locale: "en-AU",
  },
};

export interface FormatOptions {
  compact?: boolean;
  showSymbol?: boolean;
  precision?: number;
}

export class ResultFormatter {
  // 格式化货币
  static formatCurrency(
    amount: number,
    currency: SupportedCurrency,
    options: FormatOptions = {},
  ): string {
    const { compact = false, showSymbol = true, precision = 2 } = options;

    const info = currencyInfo[currency];

    if (!info) {
      throw new Error(`Unsupported currency: ${currency}`);
    }

    // 处理紧凑格式
    if (compact && Math.abs(amount) >= 1000) {
      return this.formatCompactCurrency(amount, currency, showSymbol);
    }

    // 标准格式化
    const formatter = new Intl.NumberFormat(info.locale, {
      style: "currency",
      currency: currency,
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    let formatted = formatter.format(amount);

    // 如果不显示符号，移除货币符号
    if (!showSymbol) {
      formatted = formatted.replace(/[^\d.,\s-]/g, "").trim();
    }

    return formatted;
  }

  // 紧凑格式化（如 $1.2M, $45.3K）
  private static formatCompactCurrency(
    amount: number,
    currency: SupportedCurrency,
    showSymbol: boolean,
  ): string {
    const info = currencyInfo[currency];
    const absAmount = Math.abs(amount);

    let value: number;
    let suffix: string;

    if (absAmount >= 1e9) {
      value = amount / 1e9;
      suffix = "B";
    } else if (absAmount >= 1e6) {
      value = amount / 1e6;
      suffix = "M";
    } else if (absAmount >= 1e3) {
      value = amount / 1e3;
      suffix = "K";
    } else {
      return this.formatCurrency(amount, currency, {
        showSymbol,
        precision: 2,
      });
    }

    const formatted = value.toFixed(1);
    const symbol = showSymbol ? info.symbol : "";

    return `${symbol}${formatted}${suffix}`;
  }

  // 格式化百分比
  static formatPercentage(value: number, precision: number = 2): string {
    return `${value.toFixed(precision)}%`;
  }

  // 格式化数字
  static formatNumber(value: number, precision: number = 0): string {
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value);
  }

  // 格式化年度分解数据
  static formatYearlyBreakdown(
    breakdown: YearlyBreakdown[],
    currency: SupportedCurrency,
  ): FormattedResult[] {
    return breakdown.map((item) => ({
      ...item,
      formatted: {
        startingBalance: this.formatCurrency(item.startingBalance, currency),
        contribution: this.formatCurrency(item.contribution, currency),
        interest: this.formatCurrency(item.interest, currency),
        endingBalance: this.formatCurrency(item.endingBalance, currency),
      },
    }));
  }

  // 格式化月度分解数据
  static formatMonthlyBreakdown(
    breakdown: MonthlyBreakdown[],
    currency: SupportedCurrency,
  ): FormattedResult[] {
    return breakdown.map((item) => ({
      ...item,
      formatted: {
        contribution: this.formatCurrency(item.contribution, currency),
        balance: this.formatCurrency(item.balance, currency),
        cumulativeContributions: this.formatCurrency(
          item.cumulativeContributions,
          currency,
        ),
        cumulativeGains: this.formatCurrency(item.cumulativeGains, currency),
      },
    }));
  }

  // 格式化计算结果摘要
  static formatResultSummary(
    result: BaseCalculationResult,
    currency: SupportedCurrency,
  ): Record<string, string> {
    return {
      finalAmount: this.formatCurrency(result.finalAmount, currency),
      totalContributions: this.formatCurrency(
        result.totalContributions,
        currency,
      ),
      totalGains: this.formatCurrency(result.totalGains, currency),
      effectiveRate: this.formatPercentage(result.effectiveRate * 100),
      gainPercentage: this.formatPercentage(
        (result.totalGains / result.totalContributions) * 100,
      ),
    };
  }

  // 格式化一次性投资结果
  static formatLumpSumResult(result: LumpSumResult): FormattedResult {
    const currency = result.currency as SupportedCurrency;
    const summary = this.formatResultSummary(result, currency);

    return {
      ...result,
      formatted: {
        ...summary,
        principal: this.formatCurrency(result.principal, currency),
        yearlyBreakdown: this.formatYearlyBreakdown(
          result.yearlyBreakdown,
          currency,
        ),
      },
    };
  }

  // 格式化定投结果
  static formatDCAResult(result: DCAResult): FormattedResult {
    const currency = result.currency as SupportedCurrency;
    const summary = this.formatResultSummary(result, currency);

    return {
      ...result,
      formatted: {
        ...summary,
        averageCostBasis: this.formatCurrency(
          result.averageCostBasis,
          currency,
        ),
        monthlyBreakdown: this.formatMonthlyBreakdown(
          result.monthlyBreakdown,
          currency,
        ),
      },
    };
  }

  // 格式化退休规划结果
  static formatRetirementResult(result: RetirementResult): FormattedResult {
    const currency = result.currency as SupportedCurrency;
    const summary = this.formatResultSummary(result, currency);

    return {
      ...result,
      formatted: {
        ...summary,
        shortfall: this.formatCurrency(result.shortfall, currency),
        recommendedMonthlyContribution: this.formatCurrency(
          result.recommendedMonthlyContribution,
          currency,
        ),
        inflationAdjustedGoal: this.formatCurrency(
          result.inflationAdjustedGoal,
          currency,
        ),
      },
    };
  }

  // 格式化储蓄目标结果
  static formatSavingsGoalResult(result: SavingsGoalResult): FormattedResult {
    const currency = result.currency as SupportedCurrency;
    const summary = this.formatResultSummary(result, currency);

    return {
      ...result,
      formatted: {
        ...summary,
        requiredMonthlyContribution: this.formatCurrency(
          result.requiredMonthlyContribution,
          currency,
        ),
        timeToGoal: `${result.timeToGoal.toFixed(1)} years`,
        projectedShortfall: result.projectedShortfall
          ? this.formatCurrency(result.projectedShortfall, currency)
          : null,
      },
    };
  }

  // 为图表准备数据
  static prepareChartData(
    breakdown: YearlyBreakdown[] | MonthlyBreakdown[],
  ): ChartDataPoint[] {
    return breakdown.map((item, index) => {
      if ("year" in item && "endingBalance" in item) {
        // 年度数据
        const yearlyItem = item as YearlyBreakdown;
        return {
          period: yearlyItem.year,
          balance: yearlyItem.endingBalance,
          contributions: yearlyItem.contribution,
          gains: yearlyItem.interest,
          label: `Year ${yearlyItem.year}`,
        };
      } else {
        // 月度数据
        const monthlyItem = item as MonthlyBreakdown;
        return {
          period: index + 1,
          balance: monthlyItem.balance,
          contributions: monthlyItem.cumulativeContributions,
          gains: monthlyItem.cumulativeGains,
          label: `Month ${monthlyItem.month}`,
        };
      }
    });
  }

  // 获取货币信息
  static getCurrencyInfo(currency: SupportedCurrency): CurrencyInfo {
    return currencyInfo[currency];
  }

  // 获取所有支持的货币
  static getSupportedCurrencies(): CurrencyInfo[] {
    return Object.values(currencyInfo);
  }
}

// 导出工具函数
export const formatCurrency = ResultFormatter.formatCurrency;
export const formatPercentage = ResultFormatter.formatPercentage;
export const formatNumber = ResultFormatter.formatNumber;
