"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  Target,
  Clock,
  BarChart3,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart,
} from "recharts";
import { formatCurrency } from "@/lib/utils/currency";
import type {
  InvestmentRecord,
  DCAStatistics,
  SupportedCurrency,
} from "@/lib/calculations/types";

// 图表视图
export function ChartsView({ 
  chartData, 
  currency, 
  displayCurrency 
}: {
  chartData: any[];
  currency: SupportedCurrency;
  displayCurrency: SupportedCurrency;
}) {
  return (
    <div className="space-y-6">
      {/* 累积投资图表 */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Cumulative Investment</h4>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" tick={{ fontSize: 12 }} />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip 
              formatter={(value: number) => [formatCurrency(value, currency), "Amount"]}
            />
            <Area 
              type="monotone" 
              dataKey="cumulativeAmount" 
              stroke="#3b82f6" 
              fill="#3b82f6" 
              fillOpacity={0.6} 
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* 月度投资柱状图 */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Monthly Investment Pattern</h4>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" tick={{ fontSize: 12 }} />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip 
              formatter={(value: number) => [formatCurrency(value, currency), "Amount"]}
            />
            <Bar dataKey="monthlyAmount" fill="#10b981" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

// 统计视图
export function StatisticsView({ 
  statistics, 
  currency 
}: {
  statistics: DCAStatistics;
  currency: SupportedCurrency;
}) {
  return (
    <div className="space-y-6">
      {/* 关键指标 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 border rounded-lg">
          <DollarSign className="mx-auto h-8 w-8 text-blue-500 mb-2" />
          <p className="text-sm text-muted-foreground">Total Invested</p>
          <p className="text-lg font-bold">{formatCurrency(statistics.totalInvested, currency)}</p>
        </div>
        <div className="text-center p-4 border rounded-lg">
          <Target className="mx-auto h-8 w-8 text-green-500 mb-2" />
          <p className="text-sm text-muted-foreground">Average Amount</p>
          <p className="text-lg font-bold">{formatCurrency(statistics.averageAmount, currency)}</p>
        </div>
        <div className="text-center p-4 border rounded-lg">
          <Clock className="mx-auto h-8 w-8 text-purple-500 mb-2" />
          <p className="text-sm text-muted-foreground">Frequency</p>
          <p className="text-lg font-bold">{statistics.investmentFrequency.toFixed(0)} days</p>
        </div>
        <div className="text-center p-4 border rounded-lg">
          <BarChart3 className="mx-auto h-8 w-8 text-orange-500 mb-2" />
          <p className="text-sm text-muted-foreground">Consistency</p>
          <p className="text-lg font-bold">{statistics.consistencyScore.toFixed(0)}%</p>
        </div>
      </div>

      {/* 投资范围 */}
      <div className="grid grid-cols-2 gap-4">
        <div className="p-4 border rounded-lg">
          <h5 className="font-semibold mb-2">Investment Range</h5>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Largest:</span>
              <span className="font-medium">{formatCurrency(statistics.largestInvestment, currency)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Smallest:</span>
              <span className="font-medium">{formatCurrency(statistics.smallestInvestment, currency)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Difference:</span>
              <span className="font-medium">
                {formatCurrency(statistics.largestInvestment - statistics.smallestInvestment, currency)}
              </span>
            </div>
          </div>
        </div>

        <div className="p-4 border rounded-lg">
          <h5 className="font-semibold mb-2">Investment Behavior</h5>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total Records:</span>
              <span className="font-medium">{statistics.totalRecords}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Avg Frequency:</span>
              <span className="font-medium">{statistics.investmentFrequency.toFixed(0)} days</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Consistency:</span>
              <span className={`font-medium ${
                statistics.consistencyScore >= 80 ? 'text-green-600' :
                statistics.consistencyScore >= 60 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {getConsistencyLabel(statistics.consistencyScore)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 辅助函数
export function getTypeVariant(type: string): "default" | "secondary" | "destructive" | "outline" {
  switch (type) {
    case "initial": return "default";
    case "regular": return "secondary";
    case "bonus": return "outline";
    default: return "outline";
  }
}

export function getConsistencyLabel(score: number): string {
  if (score >= 80) return "Excellent";
  if (score >= 60) return "Good";
  if (score >= 40) return "Fair";
  return "Needs Improvement";
}

// 计算统计数据
export function calculateDCAStatistics(records: InvestmentRecord[]): DCAStatistics {
  if (records.length === 0) {
    return {
      totalInvested: 0,
      totalRecords: 0,
      averageAmount: 0,
      investmentFrequency: 0,
      largestInvestment: 0,
      smallestInvestment: 0,
      monthlyTrend: [],
      consistencyScore: 0,
    };
  }

  const totalInvested = records.reduce((sum, record) => sum + record.amount, 0);
  const amounts = records.map(record => record.amount);
  const averageAmount = totalInvested / records.length;
  
  // 计算投资频率
  const sortedRecords = [...records].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  let totalDays = 0;
  for (let i = 1; i < sortedRecords.length; i++) {
    const daysDiff = (new Date(sortedRecords[i].date).getTime() - new Date(sortedRecords[i-1].date).getTime()) / (1000 * 60 * 60 * 24);
    totalDays += daysDiff;
  }
  const investmentFrequency = sortedRecords.length > 1 ? totalDays / (sortedRecords.length - 1) : 0;

  // 计算一致性评分
  const variance = amounts.reduce((sum, amount) => sum + Math.pow(amount - averageAmount, 2), 0) / amounts.length;
  const standardDeviation = Math.sqrt(variance);
  const coefficientOfVariation = standardDeviation / averageAmount;
  const consistencyScore = Math.max(0, Math.min(100, 100 - (coefficientOfVariation * 100)));

  return {
    totalInvested,
    totalRecords: records.length,
    averageAmount,
    investmentFrequency,
    largestInvestment: Math.max(...amounts),
    smallestInvestment: Math.min(...amounts),
    monthlyTrend: [], // 简化处理
    consistencyScore,
  };
}

// 准备图表数据
export function prepareChartData(records: InvestmentRecord[]): any[] {
  const sortedRecords = [...records].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  let cumulativeAmount = 0;
  return sortedRecords.map(record => {
    cumulativeAmount += record.amount;
    return {
      date: new Date(record.date).toLocaleDateString(),
      month: new Date(record.date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      amount: record.amount,
      cumulativeAmount,
      monthlyAmount: record.amount,
    };
  });
}
