"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DialogFooter } from "@/components/ui/dialog";
import {
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import {
  DollarSign,
  TrendingUp,
  PieChart,
  Edit2,
  Trash2,
  Calendar,
  Target,
  BarChart3,
} from "lucide-react";
import {
  CurrencyInputField,
  PercentageInputField,
} from "@/components/calculator/core/InputField";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { DCARecordsManager } from "./dca-records-manager";
import type {
  InvestmentPortfolio,
  AssetAllocation,
  SupportedCurrency,
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
  InvestmentRecord,
} from "@/lib/calculations/types";

// Portfolio Edit Form
export function PortfolioEditForm({
  portfolio,
  onSave,
}: {
  portfolio?: InvestmentPortfolio;
  onSave: (updates: Partial<InvestmentPortfolio>) => void;
}) {
  const [name, setName] = useState(portfolio?.name || "");
  const [description, setDescription] = useState(portfolio?.description || "");

  const handleSave = () => {
    onSave({ name, description });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="portfolio-name">Portfolio Name</Label>
        <Input
          id="portfolio-name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="My Investment Portfolio"
        />
      </div>
      <div>
        <Label htmlFor="portfolio-description">Description (Optional)</Label>
        <Input
          id="portfolio-description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your investment strategy..."
        />
      </div>
      <DialogFooter>
        <Button onClick={handleSave}>Save Changes</Button>
      </DialogFooter>
    </div>
  );
}

// Asset Form
export function AssetForm({
  onSave,
  displayCurrency,
  isDisabled = false,
  initialData,
}: {
  onSave: (data: Partial<AssetAllocation>) => void;
  displayCurrency: SupportedCurrency;
  isDisabled?: boolean;
  initialData?: AssetAllocation;
}) {
  const [name, setName] = useState(initialData?.name || "");
  const [investmentCurrency, setInvestmentCurrency] =
    useState<SupportedCurrency>(
      initialData?.investmentCurrency || displayCurrency,
    );
  const [calculationType, setCalculationType] = useState<
    "lump_sum" | "dca" | "retirement" | "savings_goal"
  >(initialData?.calculationType || "lump_sum");
  const [amount, setAmount] = useState(initialData?.amount || 10000);
  const [annualRate, setAnnualRate] = useState(
    (initialData?.params as any)?.annualRate ||
      (initialData?.params as any)?.expectedReturn ||
      7,
  );
  const [years, setYears] = useState(
    (initialData?.params as any)?.years ||
      (initialData?.params as any)?.timeframe ||
      10,
  );
  const [monthlyContribution, setMonthlyContribution] = useState(
    (initialData?.params as any)?.monthlyContribution || 500,
  );

  const handleSave = () => {
    if (!name.trim()) {
      alert("Please enter an asset name");
      return;
    }

    let params;
    switch (calculationType) {
      case "lump_sum":
        params = {
          principal: amount,
          annualRate,
          years,
          currency: investmentCurrency,
          name,
        };
        break;
      case "dca":
        params = {
          initialInvestment: amount * 0.2, // 20% initial
          monthlyContribution,
          annualRate,
          years,
          currency: investmentCurrency,
          name,
        };
        break;
      case "retirement":
        params = {
          currentAge: 30,
          retirementAge: 65,
          currentSavings: amount,
          monthlyContribution,
          expectedReturn: annualRate,
          inflationRate: 2.5,
          retirementGoal: amount * 10,
          currency: investmentCurrency,
          name,
        };
        break;
      case "savings_goal":
        params = {
          targetAmount: amount * 2,
          timeframe: years,
          initialAmount: amount,
          expectedReturn: annualRate,
          contributionFrequency: "monthly" as const,
          currency: investmentCurrency,
          name,
        };
        break;
    }

    onSave({
      name,
      investmentCurrency,
      amount,
      calculationType,
      params,
      percentage: 0, // Will be calculated later
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="asset-name">Asset Name</Label>
          <Input
            id="asset-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="US Stock Market"
          />
        </div>
        <div>
          <Label htmlFor="investment-currency">Investment Currency</Label>
          <Select
            value={investmentCurrency}
            onValueChange={(value: SupportedCurrency) =>
              setInvestmentCurrency(value)
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD ($)</SelectItem>
              <SelectItem value="EUR">EUR (€)</SelectItem>
              <SelectItem value="GBP">GBP (£)</SelectItem>
              <SelectItem value="JPY">JPY (¥)</SelectItem>
              <SelectItem value="CNY">CNY (¥)</SelectItem>
              <SelectItem value="CAD">CAD (C$)</SelectItem>
              <SelectItem value="AUD">AUD (A$)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="calculation-type">Investment Strategy</Label>
        <Select
          value={calculationType}
          onValueChange={(value: any) => setCalculationType(value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="lump_sum">Lump Sum Investment</SelectItem>
            <SelectItem value="dca">Dollar Cost Averaging</SelectItem>
            <SelectItem value="retirement">Retirement Planning</SelectItem>
            <SelectItem value="savings_goal">Savings Goal</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <CurrencyInputField
        label={
          calculationType === "dca" ? "Initial Investment" : "Investment Amount"
        }
        value={amount}
        onChange={setAmount}
        currency={investmentCurrency}
        min={1}
        placeholder="10,000"
      />

      {calculationType === "dca" && (
        <CurrencyInputField
          label="Monthly Contribution"
          value={monthlyContribution}
          onChange={setMonthlyContribution}
          currency={investmentCurrency}
          min={1}
          placeholder="500"
        />
      )}

      <PercentageInputField
        label="Expected Annual Return"
        value={annualRate}
        onChange={setAnnualRate}
        min={0}
        max={50}
        showSlider
      />

      <div className="space-y-2">
        <Label>Investment Period (Years)</Label>
        <div className="flex items-center gap-4">
          <input
            type="range"
            min="1"
            max="50"
            value={years}
            onChange={(e) => setYears(parseInt(e.target.value))}
            className="flex-1"
          />
          <span className="w-12 text-sm font-medium">{years} yrs</span>
        </div>
      </div>

      <DialogFooter>
        <Button onClick={handleSave} disabled={isDisabled}>
          {isDisabled ? "Upgrade Required" : "Add Asset"}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Assets View
export function AssetsView({
  portfolio,
  displayCurrency,
  onUpdateAsset,
  onRemoveAsset,
  onEditAsset,
}: {
  portfolio?: InvestmentPortfolio;
  displayCurrency: SupportedCurrency;
  onUpdateAsset: (assetId: string, updates: Partial<AssetAllocation>) => void;
  onRemoveAsset: (assetId: string) => void;
  onEditAsset: (asset: AssetAllocation) => void;
}) {
  if (!portfolio) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Asset Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Investment</TableHead>
              <TableHead>Final Value</TableHead>
              <TableHead>Gains</TableHead>
              <TableHead>Return %</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {portfolio.assets.map((asset) => (
              <TableRow key={asset.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div
                      className="h-3 w-3 rounded-full"
                      style={{ backgroundColor: asset.color }}
                    />
                    <span className="font-medium">{asset.name}</span>
                  </div>
                </TableCell>
                <TableCell className="capitalize">
                  {asset.calculationType.replace("_", " ")}
                </TableCell>
                <TableCell>{asset.investmentCurrency}</TableCell>
                <TableCell>
                  {formatCurrency(asset.amount, asset.investmentCurrency)}
                </TableCell>
                <TableCell>
                  {asset.result
                    ? formatCurrency(
                        asset.result.finalAmount,
                        asset.investmentCurrency,
                      )
                    : "-"}
                </TableCell>
                <TableCell>
                  {asset.result ? (
                    <span
                      className={
                        asset.result.totalGains >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {formatCurrency(
                        asset.result.totalGains,
                        asset.investmentCurrency,
                      )}
                    </span>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  {asset.result && asset.result.totalContributions > 0 ? (
                    <span
                      className={
                        asset.result.totalGains >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {CurrencyUtils.formatPercentage(
                        (asset.result.totalGains /
                          asset.result.totalContributions) *
                          100,
                      )}
                    </span>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditAsset(asset)}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveAsset(asset.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

// Timeline View
export function TimelineView({
  portfolio,
  timelineData,
  displayCurrency,
  onUpdateAssetRecords,
}: {
  portfolio?: InvestmentPortfolio;
  timelineData: any[];
  displayCurrency: SupportedCurrency;
  onUpdateAssetRecords?: (assetId: string, records: InvestmentRecord[]) => void;
}) {
  if (!portfolio) return null;

  const [timeScale, setTimeScale] = useState<"monthly" | "yearly">("yearly");

  // Filter data based on time scale
  const filteredData =
    timeScale === "yearly"
      ? timelineData.filter((_, index) => index % 12 === 0).slice(0, 11)
      : timelineData.slice(0, 61); // 5 years monthly

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Investment Growth Timeline
            </div>
            <Select
              value={timeScale}
              onValueChange={(value: "monthly" | "yearly") =>
                setTimeScale(value)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={filteredData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={timeScale === "yearly" ? "year" : "month"}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickFormatter={(value) =>
                  formatCurrency(value, displayCurrency, { compact: true })
                }
              />
              <Tooltip
                formatter={(value: number, name: string) => [
                  formatCurrency(value, displayCurrency),
                  name,
                ]}
                labelFormatter={(label) =>
                  timeScale === "yearly" ? label : `Month ${label}`
                }
              />
              <Legend />
              {portfolio.assets.map((asset, index) => (
                <Area
                  key={asset.id}
                  type="monotone"
                  dataKey={asset.name}
                  stackId="1"
                  stroke={asset.color}
                  fill={asset.color}
                  fillOpacity={0.6}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* DCA Investment Records Management */}
      {portfolio.assets
        .filter((asset) => asset.calculationType === "dca")
        .map((asset) => (
          <DCARecordsManager
            key={asset.id}
            asset={asset}
            displayCurrency={displayCurrency}
            onUpdateRecords={(records) => {
              if (onUpdateAssetRecords) {
                onUpdateAssetRecords(asset.id, records);
              }
            }}
          />
        ))}
    </div>
  );
}

// Overview View
export function OverviewView({
  portfolio,
  pieChartData,
  displayCurrency,
}: {
  portfolio?: InvestmentPortfolio;
  pieChartData: any[];
  displayCurrency: SupportedCurrency;
}) {
  if (!portfolio) return null;

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="grid gap-8 lg:grid-cols-2">
      {/* Portfolio Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Portfolio Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-muted-foreground text-sm">
                Total Portfolio Value
              </p>
              <p className="text-primary text-3xl font-bold">
                {formatCurrency(portfolio.totalValue || 0, displayCurrency, {
                  compact: true,
                })}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-muted-foreground text-sm">Total Gains</p>
                <p className="text-xl font-semibold text-green-600">
                  {formatCurrency(portfolio.totalGains || 0, displayCurrency, {
                    compact: true,
                  })}
                </p>
              </div>
              <div className="text-center">
                <p className="text-muted-foreground text-sm">Total Return</p>
                <p className="text-xl font-semibold">
                  {CurrencyUtils.formatPercentage(portfolio.totalReturn || 0)}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Assets</span>
                <span className="font-medium">{portfolio.assets.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Display Currency</span>
                <span className="font-medium">{portfolio.displayCurrency}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Last Updated</span>
                <span className="font-medium">
                  {portfolio.updatedAt.toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Asset Allocation Pie Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Asset Allocation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsPieChart>
              <Pie
                data={pieChartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value: number, name: string, props: any) => [
                  formatCurrency(value, props.payload.currency),
                  name,
                ]}
              />
              <Legend />
            </RechartsPieChart>
          </ResponsiveContainer>

          {/* Legend */}
          <div className="mt-4 space-y-2">
            {pieChartData.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-sm"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="h-3 w-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span>{item.name}</span>
                </div>
                <span className="font-medium">
                  {formatCurrency(item.value, item.currency, { compact: true })}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
