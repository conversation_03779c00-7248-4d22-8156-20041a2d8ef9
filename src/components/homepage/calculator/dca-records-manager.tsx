"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Edit2,
  Trash2,
  Calendar,
  TrendingUp,
  BarChart3,
  PieChart,
  Target,
  DollarSign,
  Clock,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>spons<PERSON><PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  Bar,
  Area,
  AreaChart,
} from "recharts";
import { CurrencyInputField } from "@/components/calculator/core/InputField";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import {
  ChartsView,
  StatisticsView,
  getTypeVariant,
  calculateDCAStatistics,
  prepareChartData,
} from "./dca-components";
import type {
  InvestmentRecord,
  DCAStatistics,
  SupportedCurrency,
  AssetAllocation,
} from "@/lib/calculations/types";

interface DCARecordsManagerProps {
  asset: AssetAllocation;
  displayCurrency: SupportedCurrency;
  onUpdateRecords: (records: InvestmentRecord[]) => void;
}

export function DCARecordsManager({
  asset,
  displayCurrency,
  onUpdateRecords,
}: DCARecordsManagerProps) {
  const [records, setRecords] = useState<InvestmentRecord[]>(
    asset.records || [],
  );
  const [isAddRecordOpen, setIsAddRecordOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<InvestmentRecord | null>(
    null,
  );
  const [viewMode, setViewMode] = useState<"list" | "chart" | "stats">("list");

  // 计算统计数据
  const statistics = calculateDCAStatistics(records);

  // 准备图表数据
  const chartData = prepareChartData(records);

  // 添加投资记录
  const addRecord = (recordData: Partial<InvestmentRecord>) => {
    const newRecord: InvestmentRecord = {
      id: Date.now().toString(),
      date: recordData.date || new Date(),
      amount: recordData.amount || 0,
      currency: recordData.currency || asset.investmentCurrency,
      type: recordData.type || "manual",
      note: recordData.note || "",
      exchangeRate: recordData.exchangeRate || 1,
      marketPrice: recordData.marketPrice,
      shares: recordData.shares,
      fees: recordData.fees || 0,
    };

    const updatedRecords = [...records, newRecord].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    );

    setRecords(updatedRecords);
    onUpdateRecords(updatedRecords);
    setIsAddRecordOpen(false);
  };

  // 更新投资记录
  const updateRecord = (
    recordId: string,
    updates: Partial<InvestmentRecord>,
  ) => {
    const updatedRecords = records.map((record) =>
      record.id === recordId ? { ...record, ...updates } : record,
    );
    setRecords(updatedRecords);
    onUpdateRecords(updatedRecords);
    setEditingRecord(null);
  };

  // 删除投资记录
  const deleteRecord = (recordId: string) => {
    const updatedRecords = records.filter((record) => record.id !== recordId);
    setRecords(updatedRecords);
    onUpdateRecords(updatedRecords);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            DCA Investment Records - {asset.name}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Select
              value={viewMode}
              onValueChange={(value: "list" | "chart" | "stats") =>
                setViewMode(value)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="list">Records</SelectItem>
                <SelectItem value="chart">Charts</SelectItem>
                <SelectItem value="stats">Statistics</SelectItem>
              </SelectContent>
            </Select>

            <Dialog open={isAddRecordOpen} onOpenChange={setIsAddRecordOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Record
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Investment Record</DialogTitle>
                  <DialogDescription>
                    Record your actual investment for {asset.name}
                  </DialogDescription>
                </DialogHeader>
                <InvestmentRecordForm
                  onSave={addRecord}
                  currency={asset.investmentCurrency}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {viewMode === "list" && (
          <RecordsListView
            records={records}
            currency={asset.investmentCurrency}
            onEdit={setEditingRecord}
            onDelete={deleteRecord}
          />
        )}

        {viewMode === "chart" && (
          <ChartsView
            chartData={chartData}
            currency={asset.investmentCurrency}
            displayCurrency={displayCurrency}
          />
        )}

        {viewMode === "stats" && (
          <StatisticsView
            statistics={statistics}
            currency={asset.investmentCurrency}
          />
        )}

        {/* Edit Record Dialog */}
        {editingRecord && (
          <Dialog
            open={!!editingRecord}
            onOpenChange={() => setEditingRecord(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Investment Record</DialogTitle>
              </DialogHeader>
              <InvestmentRecordForm
                initialData={editingRecord}
                onSave={(updates) => updateRecord(editingRecord.id, updates)}
                currency={asset.investmentCurrency}
              />
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  );
}

// 投资记录表单
function InvestmentRecordForm({
  onSave,
  currency,
  initialData,
}: {
  onSave: (data: Partial<InvestmentRecord>) => void;
  currency: SupportedCurrency;
  initialData?: InvestmentRecord;
}) {
  const [date, setDate] = useState(
    initialData?.date
      ? new Date(initialData.date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
  );
  const [amount, setAmount] = useState(initialData?.amount || 0);
  const [type, setType] = useState<"initial" | "regular" | "manual" | "bonus">(
    initialData?.type || "manual",
  );
  const [note, setNote] = useState(initialData?.note || "");
  const [marketPrice, setMarketPrice] = useState(initialData?.marketPrice || 0);
  const [fees, setFees] = useState(initialData?.fees || 0);

  const handleSave = () => {
    if (amount <= 0) {
      alert("Please enter a valid investment amount");
      return;
    }

    const shares = marketPrice > 0 ? amount / marketPrice : undefined;

    onSave({
      date: new Date(date),
      amount,
      currency,
      type,
      note,
      marketPrice: marketPrice > 0 ? marketPrice : undefined,
      shares,
      fees,
    });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="investment-date">Investment Date</Label>
          <Input
            id="investment-date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="investment-type">Investment Type</Label>
          <Select value={type} onValueChange={(value: any) => setType(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="initial">Initial Investment</SelectItem>
              <SelectItem value="regular">Regular DCA</SelectItem>
              <SelectItem value="manual">Manual Investment</SelectItem>
              <SelectItem value="bonus">Bonus Investment</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <CurrencyInputField
        label="Investment Amount"
        value={amount}
        onChange={setAmount}
        currency={currency}
        min={0.01}
        placeholder="1,000"
      />

      <div className="grid grid-cols-2 gap-4">
        <CurrencyInputField
          label="Market Price (Optional)"
          value={marketPrice}
          onChange={setMarketPrice}
          currency={currency}
          min={0}
          placeholder="100.50"
        />
        <CurrencyInputField
          label="Fees (Optional)"
          value={fees}
          onChange={setFees}
          currency={currency}
          min={0}
          placeholder="5.00"
        />
      </div>

      <div>
        <Label htmlFor="investment-note">Note (Optional)</Label>
        <Input
          id="investment-note"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Monthly investment, market dip purchase, etc."
        />
      </div>

      {marketPrice > 0 && amount > 0 && (
        <div className="bg-muted/50 rounded-lg p-3">
          <p className="text-muted-foreground text-sm">
            Shares purchased:{" "}
            <span className="font-medium">
              {(amount / marketPrice).toFixed(4)}
            </span>
          </p>
          <p className="text-muted-foreground text-sm">
            Net investment:{" "}
            <span className="font-medium">
              {formatCurrency(amount + fees, currency)}
            </span>
          </p>
        </div>
      )}

      <DialogFooter>
        <Button onClick={handleSave}>
          {initialData ? "Update Record" : "Add Record"}
        </Button>
      </DialogFooter>
    </div>
  );
}

// 记录列表视图
function RecordsListView({
  records,
  currency,
  onEdit,
  onDelete,
}: {
  records: InvestmentRecord[];
  currency: SupportedCurrency;
  onEdit: (record: InvestmentRecord) => void;
  onDelete: (recordId: string) => void;
}) {
  if (records.length === 0) {
    return (
      <div className="py-8 text-center">
        <Calendar className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <p className="text-muted-foreground">No investment records yet</p>
        <p className="text-muted-foreground text-sm">
          Add your first investment record to start tracking
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center">
          <p className="text-muted-foreground text-sm">Total Invested</p>
          <p className="text-lg font-semibold">
            {formatCurrency(
              records.reduce(
                (sum, record) => sum + record.amount + (record.fees || 0),
                0,
              ),
              currency,
            )}
          </p>
        </div>
        <div className="text-center">
          <p className="text-muted-foreground text-sm">Total Records</p>
          <p className="text-lg font-semibold">{records.length}</p>
        </div>
        <div className="text-center">
          <p className="text-muted-foreground text-sm">Average Amount</p>
          <p className="text-lg font-semibold">
            {formatCurrency(
              records.reduce((sum, record) => sum + record.amount, 0) /
                records.length,
              currency,
            )}
          </p>
        </div>
      </div>

      {/* Records Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Shares</TableHead>
            <TableHead>Note</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {records.map((record) => (
            <TableRow key={record.id}>
              <TableCell>
                {new Date(record.date).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <Badge variant={getTypeVariant(record.type)}>
                  {record.type.replace("_", " ")}
                </Badge>
              </TableCell>
              <TableCell className="font-medium">
                {formatCurrency(record.amount, currency)}
              </TableCell>
              <TableCell>
                {record.marketPrice
                  ? formatCurrency(record.marketPrice, currency)
                  : "-"}
              </TableCell>
              <TableCell>
                {record.shares ? record.shares.toFixed(4) : "-"}
              </TableCell>
              <TableCell className="max-w-32 truncate">
                {record.note || "-"}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(record)}
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
