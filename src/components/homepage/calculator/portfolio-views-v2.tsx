"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DollarSign,
  TrendingUp,
  Target,
  Calendar,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  <PERSON><PERSON>hart,
  Plus,
  Briefcase,
} from "lucide-react";
import {
  Pie<PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";

// 类型定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  investmentMethod: "lump_sum" | "dca" | "mixed";
  currency: string;
  targetAllocation: number;
  currentValue: number;
  totalInvested: number;
  totalShares: number;
  averageCost: number;
  isVisible: boolean;
  color: string;
  records: InvestmentRecord[];
  unrealizedGain: number;
  unrealizedGainPercent: number;
  realizedGain: number;
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: string;
  holdings: InvestmentHolding[];
  totalValue: number;
  totalInvested: number;
  totalGain: number;
  totalGainPercent: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Overview View
export function OverviewView({
  portfolio,
  onEditPortfolio,
  onToggleHoldingVisibility,
}: {
  portfolio: Portfolio;
  onEditPortfolio: (portfolio: Portfolio) => void;
  onToggleHoldingVisibility: (holdingId: string) => void;
}) {
  // 准备饼图数据
  const pieData = portfolio.holdings.map((holding) => ({
    name: holding.symbol || holding.name,
    value: holding.currentValue,
    color: holding.color,
    allocation: holding.targetAllocation,
    actualAllocation:
      portfolio.totalValue > 0
        ? (holding.currentValue / portfolio.totalValue) * 100
        : 0,
  }));

  // 准备时间线数据（模拟）
  const timelineData = generateTimelineData(portfolio.holdings);

  return (
    <div className="space-y-8">
      {/* Portfolio Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                {portfolio.name}
              </CardTitle>
              <p className="text-muted-foreground mt-1">
                {portfolio.description}
              </p>
              <div className="mt-2 flex items-center gap-4">
                <Badge
                  variant={
                    portfolio.riskLevel === "conservative"
                      ? "secondary"
                      : portfolio.riskLevel === "moderate"
                        ? "default"
                        : "destructive"
                  }
                >
                  {portfolio.riskLevel}
                </Badge>
                <span className="text-muted-foreground text-sm">
                  {portfolio.strategy}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditPortfolio(portfolio)}
            >
              <Edit2 className="mr-2 h-4 w-4" />
              Edit Portfolio
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-4">
            <div className="text-center">
              <DollarSign className="mx-auto mb-2 h-8 w-8 text-blue-500" />
              <p className="text-muted-foreground text-sm">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(portfolio.totalValue, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <TrendingUp className="mx-auto mb-2 h-8 w-8 text-green-500" />
              <p className="text-muted-foreground text-sm">Total Gain</p>
              <p
                className={`text-2xl font-bold ${portfolio.totalGain >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {formatCurrency(portfolio.totalGain, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <BarChart3 className="mx-auto mb-2 h-8 w-8 text-purple-500" />
              <p className="text-muted-foreground text-sm">Return %</p>
              <p
                className={`text-2xl font-bold ${portfolio.totalGainPercent >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {CurrencyUtils.formatPercentage(portfolio.totalGainPercent)}
              </p>
            </div>
            <div className="text-center">
              <Target className="mx-auto mb-2 h-8 w-8 text-orange-500" />
              <p className="text-muted-foreground text-sm">Holdings</p>
              <p className="text-2xl font-bold">{portfolio.holdings.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Allocation Pie Chart */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Asset Allocation
              </CardTitle>
              <div className="flex gap-2">
                {portfolio.holdings.map((holding) => (
                  <Button
                    key={holding.id}
                    variant={holding.isVisible ? "default" : "outline"}
                    size="sm"
                    onClick={() => onToggleHoldingVisibility(holding.id)}
                    className="flex items-center gap-1"
                  >
                    <div
                      className="h-2 w-2 rounded-full"
                      style={{ backgroundColor: holding.color }}
                    />
                    {holding.isVisible ? (
                      <Eye className="h-3 w-3" />
                    ) : (
                      <EyeOff className="h-3 w-3" />
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={pieData.filter(
                    (_, index) => portfolio.holdings[index].isVisible,
                  )}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ actualAllocation }) =>
                    `${actualAllocation.toFixed(1)}%`
                  }
                >
                  {pieData
                    .filter((_, index) => portfolio.holdings[index].isVisible)
                    .map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [
                    formatCurrency(value, portfolio.currency),
                    "Value",
                  ]}
                />
                <Legend />
              </RechartsPieChart>
            </ResponsiveContainer>

            {/* Allocation vs Target */}
            <div className="mt-4 space-y-2">
              {portfolio.holdings
                .filter((h) => h.isVisible)
                .map((holding) => {
                  const actualAllocation =
                    portfolio.totalValue > 0
                      ? (holding.currentValue / portfolio.totalValue) * 100
                      : 0;
                  const difference =
                    actualAllocation - holding.targetAllocation;

                  return (
                    <div
                      key={holding.id}
                      className="flex items-center justify-between text-sm"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: holding.color }}
                        />
                        <span>{holding.symbol || holding.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span>{actualAllocation.toFixed(1)}%</span>
                        <span className="text-muted-foreground">vs</span>
                        <span>{holding.targetAllocation}%</span>
                        <span
                          className={`font-medium ${
                            Math.abs(difference) < 1
                              ? "text-green-600"
                              : Math.abs(difference) < 5
                                ? "text-yellow-600"
                                : "text-red-600"
                          }`}
                        >
                          ({difference > 0 ? "+" : ""}
                          {difference.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Portfolio Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={timelineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [
                    formatCurrency(value, portfolio.currency),
                    "Value",
                  ]}
                />
                <Legend />
                {portfolio.holdings
                  .filter((h) => h.isVisible)
                  .map((holding) => (
                    <Area
                      key={holding.id}
                      type="monotone"
                      dataKey={holding.symbol || holding.name}
                      stackId="1"
                      stroke={holding.color}
                      fill={holding.color}
                      fillOpacity={0.6}
                    />
                  ))}
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Holdings Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Holdings Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Holding</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Gain/Loss</TableHead>
                <TableHead>Allocation</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {portfolio.holdings.map((holding) => {
                const actualAllocation =
                  portfolio.totalValue > 0
                    ? (holding.currentValue / portfolio.totalValue) * 100
                    : 0;

                return (
                  <TableRow key={holding.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: holding.color }}
                        />
                        <div>
                          <p className="font-medium">{holding.name}</p>
                          {holding.symbol && (
                            <p className="text-muted-foreground text-sm">
                              {holding.symbol}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {holding.type.replace("_", " ").toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          holding.investmentMethod === "dca"
                            ? "default"
                            : "secondary"
                        }
                      >
                        {holding.investmentMethod === "dca"
                          ? "DCA"
                          : holding.investmentMethod === "lump_sum"
                            ? "Lump Sum"
                            : "Mixed"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(holding.currentValue, holding.currency)}
                    </TableCell>
                    <TableCell>
                      <div
                        className={
                          holding.unrealizedGain >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {formatCurrency(
                          holding.unrealizedGain,
                          holding.currency,
                        )}
                        <br />
                        <span className="text-xs">
                          (
                          {CurrencyUtils.formatPercentage(
                            holding.unrealizedGainPercent,
                          )}
                          )
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <span>{actualAllocation.toFixed(1)}%</span>
                        <span className="text-muted-foreground">
                          {" "}
                          / {holding.targetAllocation}%
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

// 生成模拟时间线数据
function generateTimelineData(holdings: InvestmentHolding[]): any[] {
  const months = 12;
  const data = [];

  for (let i = 0; i < months; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - (months - 1 - i));

    const dataPoint: any = {
      date: date.toLocaleDateString("en-US", { month: "short" }),
    };

    holdings.forEach((holding) => {
      // 基于投资记录模拟增长
      const growth = 1 + (Math.random() * 0.2 - 0.1); // -10% to +10% monthly variation
      const baseValue = holding.totalInvested * (1 + i * 0.02); // 2% monthly growth trend
      dataPoint[holding.symbol || holding.name] = baseValue * growth;
    });

    data.push(dataPoint);
  }

  return data;
}

// Holdings View
export function HoldingsView({
  portfolio,
  onEditHolding,
  onDeleteHolding,
  onToggleVisibility,
}: {
  portfolio: Portfolio;
  onEditHolding: (holding: InvestmentHolding) => void;
  onDeleteHolding: (holdingId: string) => void;
  onToggleVisibility: (holdingId: string) => void;
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Investment Holdings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Holding</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Currency</TableHead>
                <TableHead>Target %</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Total Invested</TableHead>
                <TableHead>Gain/Loss</TableHead>
                <TableHead>Shares</TableHead>
                <TableHead>Avg Cost</TableHead>
                <TableHead>Visibility</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {portfolio.holdings.map((holding) => (
                <TableRow key={holding.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: holding.color }}
                      />
                      <div>
                        <p className="font-medium">{holding.name}</p>
                        {holding.symbol && (
                          <p className="text-muted-foreground text-sm">
                            {holding.symbol}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {holding.type.replace("_", " ").toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        holding.investmentMethod === "dca"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {holding.investmentMethod === "dca"
                        ? "DCA"
                        : holding.investmentMethod === "lump_sum"
                          ? "Lump Sum"
                          : "Mixed"}
                    </Badge>
                  </TableCell>
                  <TableCell>{holding.currency}</TableCell>
                  <TableCell>{holding.targetAllocation}%</TableCell>
                  <TableCell>
                    {formatCurrency(holding.currentValue, holding.currency)}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(holding.totalInvested, holding.currency)}
                  </TableCell>
                  <TableCell>
                    <div
                      className={
                        holding.unrealizedGain >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {formatCurrency(holding.unrealizedGain, holding.currency)}
                      <br />
                      <span className="text-xs">
                        (
                        {CurrencyUtils.formatPercentage(
                          holding.unrealizedGainPercent,
                        )}
                        )
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{holding.totalShares.toFixed(3)}</TableCell>
                  <TableCell>
                    {formatCurrency(holding.averageCost, holding.currency)}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleVisibility(holding.id)}
                    >
                      {holding.isVisible ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEditHolding(holding)}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteHolding(holding.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Detailed Holdings Cards */}
      <div className="grid gap-6 md:grid-cols-2">
        {portfolio.holdings.map((holding) => (
          <Card key={holding.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <div
                      className="h-4 w-4 rounded-full"
                      style={{ backgroundColor: holding.color }}
                    />
                    {holding.name}
                  </CardTitle>
                  {holding.symbol && (
                    <p className="text-muted-foreground">{holding.symbol}</p>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEditHolding(holding)}
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-muted-foreground text-sm">
                      Current Value
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(holding.currentValue, holding.currency)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">
                      Total Invested
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(holding.totalInvested, holding.currency)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">
                      Unrealized Gain
                    </p>
                    <p
                      className={`text-lg font-semibold ${
                        holding.unrealizedGain >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {formatCurrency(holding.unrealizedGain, holding.currency)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Return %</p>
                    <p
                      className={`text-lg font-semibold ${
                        holding.unrealizedGainPercent >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {CurrencyUtils.formatPercentage(
                        holding.unrealizedGainPercent,
                      )}
                    </p>
                  </div>
                </div>

                {/* Investment Details */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Total Shares</p>
                    <p className="font-medium">
                      {holding.totalShares.toFixed(3)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Average Cost</p>
                    <p className="font-medium">
                      {formatCurrency(holding.averageCost, holding.currency)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Target Allocation</p>
                    <p className="font-medium">{holding.targetAllocation}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Records</p>
                    <p className="font-medium">{holding.records.length}</p>
                  </div>
                </div>

                {/* Recent Records */}
                {holding.records.length > 0 && (
                  <div>
                    <p className="mb-2 text-sm font-medium">
                      Recent Transactions
                    </p>
                    <div className="space-y-1">
                      {holding.records.slice(-3).map((record) => (
                        <div
                          key={record.id}
                          className="bg-muted/50 flex items-center justify-between rounded p-2 text-xs"
                        >
                          <div>
                            <span className="font-medium">
                              {new Date(record.date).toLocaleDateString()}
                            </span>
                            <span className="text-muted-foreground ml-2">
                              {record.type}
                            </span>
                          </div>
                          <span className="font-medium">
                            {formatCurrency(record.amount, holding.currency)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Records View
export function RecordsView({
  portfolio,
  onAddRecord,
}: {
  portfolio: Portfolio;
  onAddRecord: (holdingId: string) => void;
}) {
  return (
    <div className="space-y-6">
      {portfolio.holdings.map((holding) => (
        <Card key={holding.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <div
                  className="h-4 w-4 rounded-full"
                  style={{ backgroundColor: holding.color }}
                />
                {holding.name} - Investment Records
              </CardTitle>
              <Button size="sm" onClick={() => onAddRecord(holding.id)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Record
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {holding.records.length === 0 ? (
              <div className="py-8 text-center">
                <Calendar className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <p className="text-muted-foreground">
                  No investment records yet
                </p>
                <p className="text-muted-foreground text-sm">
                  Add your first investment record to start tracking
                </p>
              </div>
            ) : (
              <>
                {/* Records Summary */}
                <div className="mb-6 grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Total Records
                    </p>
                    <p className="text-lg font-semibold">
                      {holding.records.length}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Total Invested
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(holding.totalInvested, holding.currency)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Total Shares
                    </p>
                    <p className="text-lg font-semibold">
                      {holding.totalShares.toFixed(3)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground text-sm">
                      Average Cost
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(holding.averageCost, holding.currency)}
                    </p>
                  </div>
                </div>

                {/* Records Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Shares</TableHead>
                      <TableHead>Fees</TableHead>
                      <TableHead>Note</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {holding.records.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          {new Date(record.date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              record.type === "buy"
                                ? "default"
                                : record.type === "sell"
                                  ? "destructive"
                                  : "secondary"
                            }
                          >
                            {record.type.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(record.amount, holding.currency)}
                        </TableCell>
                        <TableCell>
                          {record.price
                            ? formatCurrency(record.price, holding.currency)
                            : "-"}
                        </TableCell>
                        <TableCell>
                          {record.shares ? record.shares.toFixed(4) : "-"}
                        </TableCell>
                        <TableCell>
                          {record.fees
                            ? formatCurrency(record.fees, holding.currency)
                            : "-"}
                        </TableCell>
                        <TableCell className="max-w-32 truncate">
                          {record.note || "-"}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
