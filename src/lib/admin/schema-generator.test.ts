import { generateZodSchema, getTableSchema } from "./schema-generator";

// Mock admin table config
jest.mock("./config", () => ({
  adminTableConfig: {
    users: { userRelated: false },
    sessions: { userRelated: true },
    testTable: { userRelated: "customUserId" },
  },
}));

// Mock database schema
jest.mock("@/database/schema", () => ({
  users: { name: "users" },
}));

// Mock drizzle core with proper mock implementation
jest.mock("drizzle-orm/pg-core", () => ({
  getTableConfig: jest.fn(),
}));

describe("schema-generator", () => {
  describe("generateZodSchema", () => {
    it("should generate schema for string columns", () => {
      const schemaInfo = [
        {
          name: "title",
          type: "string" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.shape.title).toBeDefined();
      expect(result.safeParse({ title: "test" }).success).toBe(true);
      expect(result.safeParse({ title: "" }).success).toBe(false);
    });

    it("should generate schema for optional string columns", () => {
      const schemaInfo = [
        {
          name: "description",
          type: "string" as const,
          isOptional: true,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ description: "test" }).success).toBe(true);
      expect(result.safeParse({ description: null }).success).toBe(true);
      expect(result.safeParse({}).success).toBe(true);
    });

    it("should generate schema for number columns", () => {
      const schemaInfo = [
        {
          name: "count",
          type: "number" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ count: 42 }).success).toBe(true);
      expect(result.safeParse({ count: "42" }).success).toBe(true); // coerce
    });

    it("should generate schema for boolean columns", () => {
      const schemaInfo = [
        {
          name: "active",
          type: "boolean" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ active: true }).success).toBe(true);
      expect(result.safeParse({ active: false }).success).toBe(true);
      expect(result.safeParse({}).success).toBe(true); // has default
    });

    it("should generate schema for date columns", () => {
      const schemaInfo = [
        {
          name: "createdAt",
          type: "date" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ createdAt: new Date() }).success).toBe(true);
      expect(result.safeParse({ createdAt: "2023-01-01" }).success).toBe(true);
      expect(result.safeParse({ createdAt: "" }).success).toBe(true);
      expect(result.safeParse({}).success).toBe(true);
    });

    it("should generate schema for enum columns with values", () => {
      const schemaInfo = [
        {
          name: "status",
          type: "enum" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
          enumValues: ["active", "inactive", "pending"],
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ status: "active" }).success).toBe(true);
      expect(result.safeParse({ status: "invalid" }).success).toBe(false);
    });

    it("should generate schema for enum columns without values", () => {
      const schemaInfo = [
        {
          name: "status",
          type: "enum" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ status: "any_string" }).success).toBe(true);
    });

    it("should generate schema for json columns", () => {
      const schemaInfo = [
        {
          name: "metadata",
          type: "json" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ metadata: '{"key": "value"}' }).success).toBe(
        true,
      );
      expect(result.safeParse({ metadata: "invalid_json" }).success).toBe(
        false,
      );
      expect(result.safeParse({ metadata: "" }).success).toBe(true);
    });

    it("should generate schema for uuid columns", () => {
      const schemaInfo = [
        {
          name: "id",
          type: "uuid" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(
        result.safeParse({ id: "123e4567-e89b-12d3-a456-************" })
          .success,
      ).toBe(true);
      expect(result.safeParse({ id: "" }).success).toBe(false);
    });

    it("should generate schema for text columns", () => {
      const schemaInfo = [
        {
          name: "content",
          type: "text" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ content: "long text content" }).success).toBe(
        true,
      );
      expect(result.safeParse({ content: "" }).success).toBe(false);
    });

    it("should generate schema for user_id columns", () => {
      const schemaInfo = [
        {
          name: "userId",
          type: "user_id" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ userId: "user123" }).success).toBe(true);
      expect(result.safeParse({ userId: "" }).success).toBe(false);
    });

    it("should handle unknown column types with fallback", () => {
      const schemaInfo = [
        {
          name: "unknown",
          type: "unknown" as any,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.safeParse({ unknown: "any value" }).success).toBe(true);
    });

    it("should skip auto-generated columns", () => {
      const schemaInfo = [
        {
          name: "id",
          type: "uuid" as const,
          isOptional: true,
          isPrimaryKey: true,
          isAutoGenerated: true,
        },
      ];

      const result = generateZodSchema(schemaInfo);

      expect(result.shape.id).toBeUndefined();
    });

    it("should skip read-only columns", () => {
      const schemaInfo = [
        {
          name: "createdAt",
          type: "date" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo, ["createdAt"]);

      expect(result.shape.createdAt).toBeUndefined();
    });

    it("should handle complex schema with mixed column types", () => {
      const schemaInfo = [
        {
          name: "id",
          type: "uuid" as const,
          isOptional: true,
          isPrimaryKey: true,
          isAutoGenerated: true,
        },
        {
          name: "name",
          type: "string" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
        {
          name: "age",
          type: "number" as const,
          isOptional: true,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
        {
          name: "active",
          type: "boolean" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
        {
          name: "createdAt",
          type: "date" as const,
          isOptional: false,
          isPrimaryKey: false,
          isAutoGenerated: false,
        },
      ];

      const result = generateZodSchema(schemaInfo, ["createdAt"]);

      expect(result.shape.id).toBeUndefined(); // auto-generated
      expect(result.shape.name).toBeDefined();
      expect(result.shape.age).toBeDefined();
      expect(result.shape.active).toBeDefined();
      expect(result.shape.createdAt).toBeUndefined(); // read-only

      expect(
        result.safeParse({ name: "John", age: 30, active: true }).success,
      ).toBe(true);
      expect(result.safeParse({ name: "John" }).success).toBe(true);
      expect(result.safeParse({ name: "" }).success).toBe(false);
    });
  });

  describe("getTableSchema", () => {
    const mockTable = { name: "test_table" };
    const mockUsersTable = { name: "users" };

    beforeEach(() => {
      // Clear all mocks before each test
      jest.clearAllMocks();

      // Get the mock from the module
      const getTableConfig = jest.requireMock(
        "drizzle-orm/pg-core",
      ).getTableConfig;

      // Set up default mock implementation
      getTableConfig.mockImplementation((table) => {
        if (table === mockTable) {
          return {
            name: "test_table",
            columns: [
              {
                name: "id",
                dataType: "PgText",
                primary: true,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "text",
              },
              {
                name: "name",
                dataType: "PgText",
                primary: false,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "text",
              },
              {
                name: "description",
                dataType: "PgText",
                primary: false,
                notNull: false,
                hasDefault: false,
                getSQLType: () => "text",
              },
              {
                name: "content",
                dataType: "PgText",
                primary: false,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "text",
              },
              {
                name: "count",
                dataType: "PgInteger",
                primary: false,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "integer",
              },
              {
                name: "active",
                dataType: "PgBoolean",
                primary: false,
                notNull: true,
                hasDefault: true,
                getSQLType: () => "boolean",
              },
              {
                name: "createdAt",
                dataType: "PgTimestamp",
                primary: false,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "timestamp",
              },
              {
                name: "status",
                dataType: "PgEnum",
                primary: false,
                notNull: true,
                hasDefault: false,
                enumValues: ["active", "inactive"],
                getSQLType: () => "enum",
              },
              {
                name: "userId",
                dataType: "PgText",
                primary: false,
                notNull: true,
                hasDefault: false,
                getSQLType: () => "text",
              },
            ],
            foreignKeys: [
              {
                reference: () => ({
                  columns: [{ name: "userId" }],
                  foreignTable: mockUsersTable,
                }),
              },
            ],
          };
        }

        if (table === mockUsersTable || table?.name === "users") {
          return {
            name: "users",
            columns: [],
            foreignKeys: [],
          };
        }

        return {
          name: "test",
          columns: [],
          foreignKeys: [],
        };
      });
    });

    it("should generate schema info for a table", () => {
      const result = getTableSchema(mockTable as any, "users");

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle table with userRelated config", () => {
      const result = getTableSchema(mockTable as any, "sessions");

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      // Should detect userId as user_id type
      const userIdColumn = result.find((col) => col.name === "userId");
      expect(userIdColumn?.type).toBe("user_id");
    });

    it("should handle table with custom userRelated column", () => {
      const result = getTableSchema(mockTable as any, "testTable");

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it("should map column types correctly", () => {
      const result = getTableSchema(mockTable as any, "users");

      const columnsByName = result.reduce(
        (acc, col) => {
          acc[col.name] = col;
          return acc;
        },
        {} as Record<string, any>,
      );

      expect(columnsByName.id?.type).toBe("string");
      expect(columnsByName.name?.type).toBe("string");
      expect(columnsByName.count?.type).toBe("number");
      expect(columnsByName.active?.type).toBe("boolean");
      expect(columnsByName.createdAt?.type).toBe("date");
      expect(columnsByName.status?.type).toBe("enum");
    });

    it("should detect text type for content columns", () => {
      const result = getTableSchema(mockTable as any, "users");

      const contentColumn = result.find((col) => col.name === "content");
      expect(contentColumn?.type).toBe("text");
    });

    it("should set isOptional based on column constraints", () => {
      const result = getTableSchema(mockTable as any, "users");

      const columnsByName = result.reduce(
        (acc, col) => {
          acc[col.name] = col;
          return acc;
        },
        {} as Record<string, any>,
      );

      expect(columnsByName.id?.isOptional).toBe(true); // primary key
      expect(columnsByName.name?.isOptional).toBe(false); // not null
      expect(columnsByName.description?.isOptional).toBe(true); // nullable
      expect(columnsByName.active?.isOptional).toBe(true); // has default
    });

    it("should identify primary keys correctly", () => {
      const result = getTableSchema(mockTable as any, "users");

      const idColumn = result.find((col) => col.name === "id");
      expect(idColumn?.isPrimaryKey).toBe(true);

      const nameColumn = result.find((col) => col.name === "name");
      expect(nameColumn?.isPrimaryKey).toBe(false);
    });

    it("should identify auto-generated columns correctly", () => {
      const result = getTableSchema(mockTable as any, "users");

      const idColumn = result.find((col) => col.name === "id");
      expect(idColumn?.isAutoGenerated).toBe(false); // text primary key without default

      const activeColumn = result.find((col) => col.name === "active");
      expect(activeColumn?.isAutoGenerated).toBe(false); // has default but not primary key
    });

    it("should handle enum columns with enumValues", () => {
      const result = getTableSchema(mockTable as any, "users");

      const statusColumn = result.find((col) => col.name === "status");
      expect(statusColumn?.type).toBe("enum");
      expect(statusColumn?.enumValues).toEqual(["active", "inactive"]);
    });

    it("should handle unknown column types with fallback", () => {
      const unknownTypeTable = { name: "unknown_table" };

      // Get the mock from the module
      const getTableConfig = jest.requireMock(
        "drizzle-orm/pg-core",
      ).getTableConfig;

      // Mock getTableConfig to return unknown type
      getTableConfig.mockReturnValue({
        name: "unknown_table",
        columns: [
          {
            name: "unknown_field",
            dataType: "UnknownType",
            primary: false,
            notNull: true,
            hasDefault: false,
            getSQLType: () => "unknown",
          },
        ],
        foreignKeys: [],
      });

      const result = getTableSchema(unknownTypeTable as any, "users");

      const unknownColumn = result.find((col) => col.name === "unknown_field");
      expect(unknownColumn?.type).toBe("string"); // fallback to string
    });

    it("should handle foreign key relationships", () => {
      const result = getTableSchema(mockTable as any, "sessions");

      // The userId column should be detected as user_id type due to foreign key
      const userIdColumn = result.find((col) => col.name === "userId");
      expect(userIdColumn?.type).toBe("user_id");
    });

    it("should handle missing admin config", () => {
      const unknownTable = { name: "unknown_table" };

      // Get the mock from the module
      const getTableConfig = jest.requireMock(
        "drizzle-orm/pg-core",
      ).getTableConfig;

      getTableConfig.mockReturnValue({
        name: "unknown_table",
        columns: [
          {
            name: "id",
            dataType: "PgText",
            primary: true,
            notNull: true,
            hasDefault: false,
            getSQLType: () => "text",
          },
        ],
        foreignKeys: [],
      });

      const result = getTableSchema(unknownTable as any, "unknownTable" as any);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(1);
    });
  });
});
