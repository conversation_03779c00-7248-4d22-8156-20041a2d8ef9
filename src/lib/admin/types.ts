export interface TableConfig {
  /**
   * 指示该表是否与用户关联。
   * 如果为 string，则其值为关联 users 表的列名。
   * 如果为 true，则默认列名为 "userId"。
   * 如果为 false 或 undefined，则不进行用户关联。
   */
  userRelated?: boolean | string;
  /**
   * 在表格和表单中需要隐藏的列名数组。
   */
  hiddenColumns?: string[];
  /**
   * 在创建和编辑表单中设置为只读的列名数组。
   */
  readOnlyColumns?: string[];
}

export type ColumnType =
  | "string"
  | "number"
  | "boolean"
  | "date"
  | "enum"
  | "json"
  | "uuid"
  | "text"
  | "user_id";

export interface ColumnInfo {
  name: string;
  type: ColumnType;
  isOptional: boolean;
  isPrimaryKey: boolean;
  isAutoGenerated: boolean;
  enumValues?: string[];
}

export type SchemaInfo = ColumnInfo[];
