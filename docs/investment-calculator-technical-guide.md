# 投资收益计算器 - 技术实现指南

## 🎯 简化版计算器设计理念

基于竞争对手分析和用户需求，简化版计算器应该是一个**功能完整但不需要注册**的专业投资计算工具，具备以下特点：

### 核心设计原则

1. **专业性** - 提供银行级别的计算精度和专业界面
2. **完整性** - 包含所有四种计算模式和对比功能，不是"玩具"版本
3. **美观性** - 超越竞争对手的视觉设计和用户体验
4. **差异化** - 通过云端同步、PDF导出等高级功能引导付费

### 功能边界定义

- ✅ **包含功能**：
  - 四种完整计算模式（一次性投资、定投、退休规划、储蓄目标）
  - 多策略创建和实时对比（最多支持10个策略）
  - 专业级数据可视化（对比表格、柱状图）
  - 策略可见性控制和参数动态调整
  - 本地数据存储和基础JSON导出
  - 多币种支持和专业界面设计

- ❌ **付费功能**：
  - 云端数据同步和跨设备访问
  - 专业PDF报告生成和自定义模板
  - 无限历史记录保存（免费版限制3个）
  - 高级分析功能（通胀调整、税收计算、风险分析）
  - 投资组合管理和高级图表类型

## 🎨 专业计算器设计特点

### 核心功能亮点

1. **多策略管理系统**
   - 支持同时创建和管理多个投资策略
   - 每个策略可独立设置参数和计算类型
   - 实时策略切换和参数调整

2. **专业级对比界面**
   - 类似竞争对手的表格式对比布局
   - 支持策略可见性控制（显示/隐藏）
   - 实时计算结果更新和排序

3. **双视图模式**
   - **Calculator模式**：专注于单个策略的详细编辑
   - **Comparison模式**：多策略并排对比和图表分析

4. **响应式专业设计**
   - 桌面端：三栏布局（策略列表 | 参数表单 | 结果显示）
   - 移动端：垂直堆叠，保持完整功能
   - 超越竞争对手的视觉设计和交互体验

### 技术架构优势

1. **组件化设计**
   - `ProfessionalCalculator`：主容器组件
   - `CalculatorView`：计算器编辑视图
   - `ComparisonView`：策略对比视图
   - `ParameterForm`：动态参数表单
   - `ResultsDisplay`：结果展示组件

2. **状态管理**
   - 统一的策略状态管理
   - 实时计算结果缓存
   - 参数变化的防抖处理

3. **数据可视化**
   - 使用Recharts进行专业图表渲染
   - 支持多种图表类型（柱状图、折线图）
   - 自适应图表尺寸和主题

## 🏗️ 文件结构设计

### 完整目录结构

```
src/
├── lib/
│   ├── calculations/
│   │   ├── types.ts              # 计算参数和结果类型定义
│   │   ├── engines.ts            # 核心计算逻辑
│   │   ├── validators.ts         # 参数验证
│   │   └── formatters.ts         # 结果格式化
│   ├── storage/
│   │   ├── local-storage.ts      # 浏览器本地存储管理
│   │   ├── sync-service.ts       # 云端同步服务
│   │   └── export-import.ts      # 数据导入导出
│   ├── reports/
│   │   ├── pdf-generator.ts      # PDF报告生成
│   │   └── templates.ts          # 报告模板
│   └── utils/
│       ├── currency.ts           # 币种处理工具
│       └── date.ts               # 日期处理工具
├── components/
│   ├── homepage/
│   │   ├── hero.tsx                    # 修改后的Hero组件
│   │   ├── investment-calculator.tsx   # 简化版计算器
│   │   ├── calculator-preview.tsx      # 计算器预览卡片
│   │   └── features.tsx               # 更新后的Features组件
│   └── calculator/
│       ├── core/
│       │   ├── CalculatorEngine.tsx        # 核心计算逻辑组件
│       │   ├── InputField.tsx             # 统一输入字段组件
│       │   ├── ResultCard.tsx             # 结果展示卡片
│       │   └── ChartWrapper.tsx           # 图表包装组件
│       ├── forms/
│       │   ├── LumpSumForm.tsx            # 一次性投资表单
│       │   ├── DCAForm.tsx                # 定投表单
│       │   ├── RetirementForm.tsx         # 退休规划表单
│       │   └── SavingsGoalForm.tsx        # 储蓄目标表单
│       └── charts/
│           ├── GrowthChart.tsx            # 增长曲线图
│           ├── ComparisonChart.tsx        # 对比图表
│           └── BreakdownChart.tsx         # 构成分析图
└── app/
    ├── dashboard/
    │   ├── page.tsx                   # 修改后的Dashboard主页
    │   ├── calculator/
    │   │   ├── page.tsx              # 完整计算器页面
    │   │   ├── [type]/
    │   │   │   └── page.tsx          # 特定类型计算器页面
    │   │   └── saved/
    │   │       └── page.tsx          # 已保存的计算管理
    │   └── _components/
    │       ├── investment-summary.tsx      # 投资概览卡片
    │       ├── quick-calculator.tsx        # 快速计算器组件
    │       └── saved-calculations.tsx      # 已保存计算列表
    └── api/
        ├── calculations/
        │   ├── route.ts              # 计算CRUD API
        │   └── sync/
        │       └── route.ts          # 数据同步API
        └── reports/
            └── route.ts              # 报告生成API
```

## 🔧 核心类型定义

### 计算参数类型

```typescript
// src/lib/calculations/types.ts

export interface BaseCalculationParams {
  currency: string;
  name?: string;
}

export interface LumpSumParams extends BaseCalculationParams {
  principal: number;
  annualRate: number;
  years: number;
  compoundingFrequency?: number; // 默认12 (月复利)
}

export interface DCAParams extends BaseCalculationParams {
  initialInvestment?: number;
  monthlyContribution: number;
  annualRate: number;
  years: number;
  contributionIncrease?: number; // 年度贡献增长率
}

export interface RetirementParams extends BaseCalculationParams {
  currentAge: number;
  retirementAge: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturn: number;
  inflationRate?: number;
  retirementGoal: number;
}

export interface SavingsGoalParams extends BaseCalculationParams {
  targetAmount: number;
  timeframe: number; // 年数
  initialAmount?: number;
  expectedReturn: number;
  contributionFrequency: "monthly" | "quarterly" | "annually";
}

export type CalculationParams =
  | LumpSumParams
  | DCAParams
  | RetirementParams
  | SavingsGoalParams;
```

### 计算结果类型

```typescript
export interface BaseCalculationResult {
  finalAmount: number;
  totalContributions: number;
  totalGains: number;
  effectiveRate: number;
  calculationType: string;
  currency: string;
}

export interface LumpSumResult extends BaseCalculationResult {
  principal: number;
  yearlyBreakdown: YearlyBreakdown[];
}

export interface DCAResult extends BaseCalculationResult {
  monthlyBreakdown: MonthlyBreakdown[];
  averageCostBasis: number;
}

export interface RetirementResult extends BaseCalculationResult {
  shortfall: number;
  recommendedMonthlyContribution: number;
  inflationAdjustedGoal: number;
}

export interface SavingsGoalResult extends BaseCalculationResult {
  requiredMonthlyContribution: number;
  timeToGoal: number;
  projectedShortfall?: number;
}

export interface YearlyBreakdown {
  year: number;
  startingBalance: number;
  contribution: number;
  interest: number;
  endingBalance: number;
}

export interface MonthlyBreakdown {
  month: number;
  year: number;
  contribution: number;
  balance: number;
  cumulativeContributions: number;
  cumulativeGains: number;
}
```

## 🧮 核心计算引擎实现

### 一次性投资计算器

```typescript
// src/lib/calculations/engines.ts

export class InvestmentCalculationEngine {
  static calculateLumpSum(params: LumpSumParams): LumpSumResult {
    const { principal, annualRate, years, compoundingFrequency = 12 } = params;

    const rate = annualRate / 100;
    const n = compoundingFrequency;
    const t = years;

    // 复利公式: A = P(1 + r/n)^(nt)
    const finalAmount = principal * Math.pow(1 + rate / n, n * t);
    const totalGains = finalAmount - principal;
    const effectiveRate = Math.pow(1 + rate / n, n) - 1;

    // 生成年度分解
    const yearlyBreakdown: YearlyBreakdown[] = [];
    let currentBalance = principal;

    for (let year = 1; year <= years; year++) {
      const startingBalance = currentBalance;
      const yearlyGrowth = startingBalance * effectiveRate;
      currentBalance = startingBalance + yearlyGrowth;

      yearlyBreakdown.push({
        year,
        startingBalance,
        contribution: year === 1 ? principal : 0,
        interest: yearlyGrowth,
        endingBalance: currentBalance,
      });
    }

    return {
      finalAmount,
      totalContributions: principal,
      totalGains,
      effectiveRate,
      calculationType: "lump_sum",
      currency: params.currency,
      principal,
      yearlyBreakdown,
    };
  }

  static calculateDCA(params: DCAParams): DCAResult {
    const {
      initialInvestment = 0,
      monthlyContribution,
      annualRate,
      years,
      contributionIncrease = 0,
    } = params;

    const monthlyRate = annualRate / 100 / 12;
    const totalMonths = years * 12;

    let balance = initialInvestment;
    let totalContributions = initialInvestment;
    let currentMonthlyContribution = monthlyContribution;

    const monthlyBreakdown: MonthlyBreakdown[] = [];

    for (let month = 1; month <= totalMonths; month++) {
      // 年度贡献增长
      if (month > 1 && (month - 1) % 12 === 0 && contributionIncrease > 0) {
        currentMonthlyContribution *= 1 + contributionIncrease / 100;
      }

      // 添加月度贡献
      balance += currentMonthlyContribution;
      totalContributions += currentMonthlyContribution;

      // 计算月度收益
      balance *= 1 + monthlyRate;

      const year = Math.ceil(month / 12);
      monthlyBreakdown.push({
        month,
        year,
        contribution: currentMonthlyContribution,
        balance,
        cumulativeContributions: totalContributions,
        cumulativeGains: balance - totalContributions,
      });
    }

    const finalAmount = balance;
    const totalGains = finalAmount - totalContributions;
    const averageCostBasis = totalContributions / totalMonths;

    return {
      finalAmount,
      totalContributions,
      totalGains,
      effectiveRate: annualRate / 100,
      calculationType: "dca",
      currency: params.currency,
      monthlyBreakdown,
      averageCostBasis,
    };
  }

  static calculateRetirement(params: RetirementParams): RetirementResult {
    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyContribution,
      expectedReturn,
      inflationRate = 2.5,
      retirementGoal,
    } = params;

    const yearsToRetirement = retirementAge - currentAge;
    const monthlyRate = expectedReturn / 100 / 12;
    const totalMonths = yearsToRetirement * 12;

    // 计算当前储蓄的未来价值
    const currentSavingsFutureValue =
      currentSavings * Math.pow(1 + expectedReturn / 100, yearsToRetirement);

    // 计算定投的未来价值
    const monthlyContributionFutureValue =
      monthlyContribution *
      ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate);

    const projectedRetirementSavings =
      currentSavingsFutureValue + monthlyContributionFutureValue;

    // 通胀调整后的退休目标
    const inflationAdjustedGoal =
      retirementGoal * Math.pow(1 + inflationRate / 100, yearsToRetirement);

    const shortfall = Math.max(
      0,
      inflationAdjustedGoal - projectedRetirementSavings,
    );

    // 计算达到目标所需的月度贡献
    const requiredTotalFutureValue =
      inflationAdjustedGoal - currentSavingsFutureValue;
    const requiredMonthlyContribution =
      requiredTotalFutureValue /
      ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate);

    return {
      finalAmount: projectedRetirementSavings,
      totalContributions: currentSavings + monthlyContribution * totalMonths,
      totalGains:
        projectedRetirementSavings -
        (currentSavings + monthlyContribution * totalMonths),
      effectiveRate: expectedReturn / 100,
      calculationType: "retirement",
      currency: params.currency,
      shortfall,
      recommendedMonthlyContribution: Math.max(0, requiredMonthlyContribution),
      inflationAdjustedGoal,
    };
  }

  static calculateSavingsGoal(params: SavingsGoalParams): SavingsGoalResult {
    const {
      targetAmount,
      timeframe,
      initialAmount = 0,
      expectedReturn,
      contributionFrequency,
    } = params;

    const annualRate = expectedReturn / 100;
    let periodsPerYear: number;

    switch (contributionFrequency) {
      case "monthly":
        periodsPerYear = 12;
        break;
      case "quarterly":
        periodsPerYear = 4;
        break;
      case "annually":
        periodsPerYear = 1;
        break;
    }

    const periodRate = annualRate / periodsPerYear;
    const totalPeriods = timeframe * periodsPerYear;

    // 计算初始金额的未来价值
    const initialFutureValue =
      initialAmount * Math.pow(1 + annualRate, timeframe);

    // 计算需要通过定期贡献达到的金额
    const requiredFromContributions = targetAmount - initialFutureValue;

    // 计算所需的定期贡献
    const requiredPeriodicContribution =
      requiredFromContributions /
      ((Math.pow(1 + periodRate, totalPeriods) - 1) / periodRate);

    // 转换为月度贡献（用于显示）
    const requiredMonthlyContribution =
      contributionFrequency === "monthly"
        ? requiredPeriodicContribution
        : (requiredPeriodicContribution * periodsPerYear) / 12;

    return {
      finalAmount: targetAmount,
      totalContributions:
        initialAmount + requiredPeriodicContribution * totalPeriods,
      totalGains:
        targetAmount -
        initialAmount -
        requiredPeriodicContribution * totalPeriods,
      effectiveRate: annualRate,
      calculationType: "savings_goal",
      currency: params.currency,
      requiredMonthlyContribution,
      timeToGoal: timeframe,
      projectedShortfall: Math.max(0, requiredFromContributions),
    };
  }
}
```

## 📊 图表组件实现

### 增长曲线图

```typescript
// src/components/calculator/charts/GrowthChart.tsx

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { formatCurrency } from '@/lib/utils/currency';

interface GrowthChartProps {
  data: YearlyBreakdown[] | MonthlyBreakdown[];
  currency: string;
  showContributions?: boolean;
}

export function GrowthChart({ data, currency, showContributions = true }: GrowthChartProps) {
  const chartData = data.map((item, index) => ({
    period: 'year' in item ? item.year : Math.ceil(item.month / 12),
    balance: item.endingBalance || item.balance,
    contributions: item.cumulativeContributions ||
      (data.slice(0, index + 1).reduce((sum, d) => sum + d.contribution, 0)),
    gains: (item.endingBalance || item.balance) -
      (item.cumulativeContributions ||
       data.slice(0, index + 1).reduce((sum, d) => sum + d.contribution, 0))
  }));

  return (
    <div className="h-80 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="period"
            label={{ value: 'Years', position: 'insideBottom', offset: -10 }}
          />
          <YAxis
            tickFormatter={(value) => formatCurrency(value, currency, { compact: true })}
          />
          <Tooltip
            formatter={(value: number, name: string) => [
              formatCurrency(value, currency),
              name === 'balance' ? 'Total Balance' :
              name === 'contributions' ? 'Total Contributions' : 'Total Gains'
            ]}
            labelFormatter={(label) => `Year ${label}`}
          />
          <Legend />

          <Line
            type="monotone"
            dataKey="balance"
            stroke="#8884d8"
            strokeWidth={3}
            name="Total Balance"
          />

          {showContributions && (
            <>
              <Line
                type="monotone"
                dataKey="contributions"
                stroke="#82ca9d"
                strokeWidth={2}
                strokeDasharray="5 5"
                name="Contributions"
              />
              <Line
                type="monotone"
                dataKey="gains"
                stroke="#ffc658"
                strokeWidth={2}
                name="Investment Gains"
              />
            </>
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
```

## 💾 本地存储管理

### 本地存储服务

```typescript
// src/lib/storage/local-storage.ts

export interface StoredCalculation {
  id: string;
  name: string;
  type: string;
  parameters: CalculationParams;
  results: BaseCalculationResult;
  currency: string;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

export class LocalStorageService {
  private static readonly STORAGE_KEY = "investment_calculations";
  private static readonly MAX_FREE_CALCULATIONS = 3;

  static getCalculations(): StoredCalculation[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return [];
    }
  }

  static saveCalculation(
    calculation: Omit<StoredCalculation, "id" | "createdAt" | "updatedAt">,
  ): string {
    const calculations = this.getCalculations();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const newCalculation: StoredCalculation = {
      ...calculation,
      id,
      createdAt: now,
      updatedAt: now,
    };

    // 检查免费版限制
    if (
      !this.isPremiumUser() &&
      calculations.length >= this.MAX_FREE_CALCULATIONS
    ) {
      throw new Error(
        "Free version limited to 3 saved calculations. Upgrade to save more.",
      );
    }

    calculations.push(newCalculation);
    this.setCalculations(calculations);

    return id;
  }

  static updateCalculation(
    id: string,
    updates: Partial<StoredCalculation>,
  ): void {
    const calculations = this.getCalculations();
    const index = calculations.findIndex((calc) => calc.id === id);

    if (index === -1) {
      throw new Error("Calculation not found");
    }

    calculations[index] = {
      ...calculations[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.setCalculations(calculations);
  }

  static deleteCalculation(id: string): void {
    const calculations = this.getCalculations();
    const filtered = calculations.filter((calc) => calc.id !== id);
    this.setCalculations(filtered);
  }

  private static setCalculations(calculations: StoredCalculation[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(calculations));
    } catch (error) {
      console.error("Error writing to localStorage:", error);
      throw new Error("Failed to save calculation");
    }
  }

  private static isPremiumUser(): boolean {
    // 检查用户订阅状态的逻辑
    // 这里需要与认证系统集成
    return false; // 临时返回false
  }

  static exportData(): string {
    const calculations = this.getCalculations();
    const exportData = {
      version: "1.0",
      exportedAt: new Date().toISOString(),
      calculations,
    };

    return JSON.stringify(exportData, null, 2);
  }

  static importData(jsonData: string): void {
    try {
      const importData = JSON.parse(jsonData);

      if (!importData.calculations || !Array.isArray(importData.calculations)) {
        throw new Error("Invalid import data format");
      }

      // 验证数据结构
      for (const calc of importData.calculations) {
        if (!calc.id || !calc.type || !calc.parameters || !calc.results) {
          throw new Error("Invalid calculation data in import");
        }
      }

      this.setCalculations(importData.calculations);
    } catch (error) {
      console.error("Error importing data:", error);
      throw new Error("Failed to import data. Please check the file format.");
    }
  }
}
```

---

**注意**: 这个技术指南提供了核心实现的详细代码示例。在实际开发中，需要根据具体需求进行调整和优化。
