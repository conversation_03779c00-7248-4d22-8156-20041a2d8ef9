import { describe, it, expect, jest, beforeEach, afterEach } from "@jest/globals";
import React from "react";

// <PERSON>ck <PERSON>send before importing anything
const mockSendFunction = jest.fn();
jest.mock("resend", () => ({
  Resend: jest.fn(() => ({
    emails: {
      send: mockSendFunction,
    },
  })),
}));

// Import after mocks are set up
import { sendEmail } from "./email";

describe("Email Module", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default successful response
    mockSendFunction.mockResolvedValue({
      data: { id: "email-id-123" },
      error: null,
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe("sendEmail function", () => {
    it("should send email successfully with basic parameters", async () => {
      const email = "<EMAIL>";
      const subject = "Test Subject";
      const body = "Test email body";

      await sendEmail(email, subject, body);

      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: email,
        subject,
        react: <>{body}</>,
      });
    });

    it("should send email with React component body", async () => {
      const email = "<EMAIL>";
      const subject = "Welcome Email";
      const body = (
        <div>
          <h1>Welcome!</h1>
          <p>Thank you for joining us.</p>
        </div>
      );

      await sendEmail(email, subject, body);

      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: email,
        subject,
        react: <>{body}</>,
      });
    });

    it("should send email with string body", async () => {
      const email = "<EMAIL>";
      const subject = "Plain Text";
      const body = "This is a plain text email body.";

      await sendEmail(email, subject, body);

      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: email,
        subject,
        react: <>{body}</>,
      });
    });

    it("should handle error from Resend", async () => {
      const apiError = new Error("Resend API error");
      mockSendFunction.mockResolvedValue({
        data: null,
        error: apiError,
      });

      await expect(
        sendEmail("<EMAIL>", "Test Subject", "Test Body")
      ).rejects.toThrow("Resend API error");
    });

    it("should handle successful email sending", async () => {
      mockSendFunction.mockResolvedValue({
        data: { id: "email-sent-123" },
        error: null,
      });

      await expect(
        sendEmail("<EMAIL>", "Test Subject", "Test Body")
      ).resolves.not.toThrow();
    });
  });

  describe("Error Handling", () => {
    it("should throw error when Resend returns an error", async () => {
      const apiError = new Error("Resend API error");
      mockSendFunction.mockResolvedValue({
        data: null,
        error: apiError,
      });

      await expect(
        sendEmail("<EMAIL>", "Test Subject", "Test Body")
      ).rejects.toThrow("Resend API error");
    });

    it("should handle network errors", async () => {
      const networkError = new Error("Network error");
      mockSendFunction.mockRejectedValue(networkError);

      await expect(
        sendEmail("<EMAIL>", "Test Subject", "Test Body")
      ).rejects.toThrow("Network error");
    });
  });

  describe("Email Content", () => {
    it("should wrap body content in React fragment", async () => {
      const content = "Simple text content";
      
      await sendEmail("<EMAIL>", "Subject", content);
      
      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: "<EMAIL>",
        subject: "Subject",
        react: <>{content}</>,
      });
    });

    it("should handle React components as body", async () => {
      const component = <div><h1>Title</h1><p>Content</p></div>;
      
      await sendEmail("<EMAIL>", "Subject", component);
      
      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: "<EMAIL>",
        subject: "Subject",
        react: <>{component}</>,
      });
    });

    it("should handle null and undefined body", async () => {
      await sendEmail("<EMAIL>", "Subject", null);
      
      expect(mockSendFunction).toHaveBeenCalledWith({
        from: "UllrAI <<EMAIL>>",
        to: "<EMAIL>",
        subject: "Subject",
        react: <>{null}</>,
      });
    });
  });
});