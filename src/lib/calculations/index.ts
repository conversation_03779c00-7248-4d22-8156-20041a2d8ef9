// 投资计算器核心模块导出

// 类型定义
export type {
  // 参数类型
  BaseCalculationParams,
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
  CalculationParams,
  
  // 结果类型
  BaseCalculationResult,
  LumpSumResult,
  DCAResult,
  RetirementResult,
  SavingsGoalResult,
  
  // 分解数据类型
  YearlyBreakdown,
  MonthlyBreakdown,
  
  // 存储类型
  StoredCalculation,
  ComparisonData,
  DataConflict,
  MergeResult,
  
  // 货币类型
  SupportedCurrency,
  CurrencyInfo,
  
  // 图表类型
  ChartDataPoint,
  
  // 验证类型
  ValidationError,
  CalculationValidationResult,
  
  // 其他类型
  ReportOptions,
  UserPreferences,
  ApiResponse,
} from './types';

// 计算引擎
export { InvestmentCalculationEngine } from './engines';

// 验证器
export { 
  CalculationValidator,
  lumpSumSchema,
  dcaSchema,
  retirementSchema,
  savingsGoalSchema,
  supportedCurrencies,
} from './validators';

// 格式化工具
export { 
  ResultFormatter,
  formatCurrency,
  formatPercentage,
  formatNumber,
} from './formatters';

// 便捷的计算函数
export const calculateLumpSum = InvestmentCalculationEngine.calculateLumpSum;
export const calculateDCA = InvestmentCalculationEngine.calculateDCA;
export const calculateRetirement = InvestmentCalculationEngine.calculateRetirement;
export const calculateSavingsGoal = InvestmentCalculationEngine.calculateSavingsGoal;

// 验证函数
export const validateLumpSum = CalculationValidator.validateLumpSum;
export const validateDCA = CalculationValidator.validateDCA;
export const validateRetirement = CalculationValidator.validateRetirement;
export const validateSavingsGoal = CalculationValidator.validateSavingsGoal;
export const validateCalculation = CalculationValidator.validate;

// 格式化函数
export const formatLumpSumResult = ResultFormatter.formatLumpSumResult;
export const formatDCAResult = ResultFormatter.formatDCAResult;
export const formatRetirementResult = ResultFormatter.formatRetirementResult;
export const formatSavingsGoalResult = ResultFormatter.formatSavingsGoalResult;
export const prepareChartData = ResultFormatter.prepareChartData;

/**
 * 完整的计算流程：验证 -> 计算 -> 格式化
 */
export function performCalculation(
  type: string,
  params: CalculationParams
): {
  isValid: boolean;
  errors?: ValidationError[];
  result?: BaseCalculationResult;
  formattedResult?: any;
} {
  // 1. 验证参数
  const validation = CalculationValidator.validate(type, params);
  
  if (!validation.isValid) {
    return {
      isValid: false,
      errors: validation.errors,
    };
  }
  
  try {
    // 2. 执行计算
    const result = InvestmentCalculationEngine.calculate(type, params);
    
    // 3. 格式化结果
    let formattedResult;
    switch (type) {
      case 'lump_sum':
        formattedResult = ResultFormatter.formatLumpSumResult(result as LumpSumResult);
        break;
      case 'dca':
        formattedResult = ResultFormatter.formatDCAResult(result as DCAResult);
        break;
      case 'retirement':
        formattedResult = ResultFormatter.formatRetirementResult(result as RetirementResult);
        break;
      case 'savings_goal':
        formattedResult = ResultFormatter.formatSavingsGoalResult(result as SavingsGoalResult);
        break;
      default:
        formattedResult = result;
    }
    
    return {
      isValid: true,
      result,
      formattedResult,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [{ 
        field: 'general', 
        message: error instanceof Error ? error.message : 'Calculation failed' 
      }],
    };
  }
}

/**
 * 批量计算多个场景
 */
export function performBatchCalculations(
  calculations: Array<{ type: string; params: CalculationParams; name?: string }>
): Array<{
  name?: string;
  type: string;
  isValid: boolean;
  errors?: ValidationError[];
  result?: BaseCalculationResult;
  formattedResult?: any;
}> {
  return calculations.map(calc => ({
    name: calc.name,
    type: calc.type,
    ...performCalculation(calc.type, calc.params),
  }));
}

/**
 * 计算对比分析
 */
export function performComparisonAnalysis(
  calculations: Array<{ name: string; type: string; params: CalculationParams }>
): {
  isValid: boolean;
  results?: Array<{
    name: string;
    type: string;
    result: BaseCalculationResult;
    formattedResult: any;
  }>;
  comparison?: {
    bestPerformance: string;
    worstPerformance: string;
    averageReturn: number;
    totalRange: number;
  };
  errors?: string[];
} {
  const results = [];
  const errors = [];
  
  // 执行所有计算
  for (const calc of calculations) {
    const calcResult = performCalculation(calc.type, calc.params);
    
    if (!calcResult.isValid) {
      errors.push(`${calc.name}: ${calcResult.errors?.map(e => e.message).join(', ')}`);
      continue;
    }
    
    results.push({
      name: calc.name,
      type: calc.type,
      result: calcResult.result!,
      formattedResult: calcResult.formattedResult,
    });
  }
  
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  
  if (results.length === 0) {
    return { isValid: false, errors: ['No valid calculations to compare'] };
  }
  
  // 计算对比分析
  const returns = results.map(r => {
    const gainPercentage = r.result.totalContributions > 0 
      ? (r.result.totalGains / r.result.totalContributions) * 100 
      : 0;
    return { name: r.name, return: gainPercentage };
  });
  
  const sortedReturns = returns.sort((a, b) => b.return - a.return);
  const averageReturn = returns.reduce((sum, r) => sum + r.return, 0) / returns.length;
  const totalRange = sortedReturns[0].return - sortedReturns[sortedReturns.length - 1].return;
  
  return {
    isValid: true,
    results,
    comparison: {
      bestPerformance: sortedReturns[0].name,
      worstPerformance: sortedReturns[sortedReturns.length - 1].name,
      averageReturn,
      totalRange,
    },
  };
}

// 重新导入以避免循环依赖
import { InvestmentCalculationEngine } from './engines';
import { CalculationValidator } from './validators';
import { ResultFormatter } from './formatters';
import type { 
  CalculationParams, 
  BaseCalculationResult, 
  LumpSumResult, 
  DCAResult, 
  RetirementResult, 
  SavingsGoalResult,
  ValidationError 
} from './types';
