"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Calculator,
  TrendingUp,
  DollarSign,
  PieChart,
  Plus,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  Calendar,
  Target,
  Briefcase,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { PercentageInputField } from "@/components/calculator/core/InputField";
import { PortfolioForm, HoldingForm } from "./portfolio-forms-v2";
import {
  HoldingsView,
  PortfoliosView,
  ComparisonView,
} from "./portfolio-views-v5";
import { IntegratedHoldingManager } from "./integrated-holding-manager";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 重新设计的数据结构
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

// 投资标的（可以独立存在，也可以加入组合）
interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  currency: SupportedCurrency;
  color: string;
  records: InvestmentRecord[];
  isVisibleInComparison: boolean; // 在对比图表中是否可见
  // 当前市场数据
  currentPrice: number; // 当前市场价格
  lastUpdated: Date; // 价格最后更新时间
  // 计算属性
  totalInvested: number;
  currentValue: number;
  totalShares: number;
  averageCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
}

// 组合中的标的配置
interface PortfolioHolding {
  holdingId: string;
  targetAllocation: number;
  isVisible: boolean;
}

// 投资组合（可选的，用于特定策略）
interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: SupportedCurrency;
  holdings: PortfolioHolding[];
  isVisibleInComparison: boolean; // 在组合对比中是否可见
  createdAt: Date;
  updatedAt: Date;
}

// 预定义颜色
const COLORS = [
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#ec4899",
  "#6366f1",
];

export function PortfolioSystemV5() {
  // 投资标的（核心数据）
  const [holdings, setHoldings] = useState<InvestmentHolding[]>([
    {
      id: "h1",
      name: "Vanguard S&P 500 ETF",
      symbol: "VOO",
      type: "etf",
      currency: "USD",
      color: COLORS[0],
      isVisibleInComparison: true,
      currentPrice: 490.0, // 当前市场价格
      lastUpdated: new Date(),
      totalInvested: 10000,
      currentValue: 12495, // 25.5 shares * $490
      totalShares: 25.5,
      averageCost: 392.16,
      unrealizedGain: 2495,
      unrealizedGainPercent: 24.95,
      records: [
        {
          id: "r1",
          date: new Date("2024-01-15"),
          amount: 5000,
          price: 400,
          shares: 12.5,
          fees: 0,
          note: "初始投资",
          type: "buy",
        },
        {
          id: "r2",
          date: new Date("2024-02-15"),
          amount: 2000,
          price: 410,
          shares: 4.88,
          fees: 0,
          note: "定期投资",
          type: "buy",
        },
        {
          id: "r3",
          date: new Date("2024-03-15"),
          amount: 3000,
          price: 380,
          shares: 7.89,
          fees: 0,
          note: "逢低加仓",
          type: "buy",
        },
      ],
    },
    {
      id: "h2",
      name: "Vanguard Total Bond Market ETF",
      symbol: "BND",
      type: "etf",
      currency: "USD",
      color: COLORS[1],
      isVisibleInComparison: true,
      currentPrice: 82.0, // 当前市场价格
      lastUpdated: new Date(),
      totalInvested: 8000,
      currentValue: 8200, // 100 shares * $82
      totalShares: 100,
      averageCost: 80,
      unrealizedGain: 200,
      unrealizedGainPercent: 2.5,
      records: [
        {
          id: "r4",
          date: new Date("2024-01-15"),
          amount: 8000,
          price: 80,
          shares: 100,
          fees: 0,
          note: "一次性投资债券",
          type: "buy",
        },
      ],
    },
  ]);

  // 投资组合（可选的）
  const [portfolios, setPortfolios] = useState<Portfolio[]>([
    {
      id: "p1",
      name: "土豆沙发组合",
      description: "简单的被动投资策略",
      strategy: "60% 股票 + 40% 债券",
      riskLevel: "moderate",
      currency: "USD",
      isVisibleInComparison: true,
      holdings: [
        { holdingId: "h1", targetAllocation: 60, isVisible: true },
        { holdingId: "h2", targetAllocation: 40, isVisible: true },
      ],
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date(),
    },
  ]);

  const [viewMode, setViewMode] = useState<
    "holdings" | "portfolios" | "comparison"
  >("holdings");
  const [comparisonMode, setComparisonMode] = useState<
    "holdings" | "portfolios"
  >("holdings");

  // 编辑状态
  const [editingHolding, setEditingHolding] =
    useState<InvestmentHolding | null>(null);
  const [editingPortfolio, setEditingPortfolio] = useState<Portfolio | null>(
    null,
  );
  const [isAddHoldingOpen, setIsAddHoldingOpen] = useState(false);
  const [isAddPortfolioOpen, setIsAddPortfolioOpen] = useState(false);
  const [managingHolding, setManagingHolding] =
    useState<InvestmentHolding | null>(null);
  const [isCreatingNewHolding, setIsCreatingNewHolding] = useState(false);

  // 添加投资标的
  const addHolding = (holdingData: Partial<InvestmentHolding>) => {
    const newHolding: InvestmentHolding = {
      id: Date.now().toString(),
      name: holdingData.name || "新投资标的",
      symbol: holdingData.symbol,
      type: holdingData.type || "etf",
      currency: holdingData.currency || "USD",
      color: COLORS[holdings.length % COLORS.length],
      records: [],
      isVisibleInComparison: true,
      currentPrice: holdingData.currentPrice || 0,
      lastUpdated: new Date(),
      totalInvested: 0,
      currentValue: 0,
      totalShares: 0,
      averageCost: 0,
      unrealizedGain: 0,
      unrealizedGainPercent: 0,
    };

    setHoldings([...holdings, newHolding]);
    setIsAddHoldingOpen(false);
  };

  // 计算真实的投资统计
  const calculateHoldingStats = (holding: InvestmentHolding) => {
    const buyRecords = holding.records.filter((r) => r.type === "buy");
    const sellRecords = holding.records.filter((r) => r.type === "sell");

    // 计算总投资和总股数
    const totalInvested = buyRecords.reduce((sum, r) => sum + r.amount, 0);
    const totalBoughtShares = buyRecords.reduce(
      (sum, r) => sum + (r.shares || 0),
      0,
    );
    const totalSoldShares = sellRecords.reduce(
      (sum, r) => sum + (r.shares || 0),
      0,
    );
    const currentShares = totalBoughtShares - totalSoldShares;

    // 计算平均成本
    const averageCost =
      totalBoughtShares > 0 ? totalInvested / totalBoughtShares : 0;

    // 计算当前价值（基于当前价格）
    const currentValue = currentShares * holding.currentPrice;

    // 计算未实现收益
    const costBasis = currentShares * averageCost;
    const unrealizedGain = currentValue - costBasis;
    const unrealizedGainPercent =
      costBasis > 0 ? (unrealizedGain / costBasis) * 100 : 0;

    return {
      totalInvested,
      currentValue,
      totalShares: currentShares,
      averageCost,
      unrealizedGain,
      unrealizedGainPercent,
    };
  };

  // 更新投资标的
  const updateHolding = (
    holdingId: string,
    updates: Partial<InvestmentHolding>,
  ) => {
    setHoldings(
      holdings.map((h) => {
        if (h.id === holdingId) {
          const updatedHolding = { ...h, ...updates };
          // 重新计算统计数据
          const stats = calculateHoldingStats(updatedHolding);
          return { ...updatedHolding, ...stats };
        }
        return h;
      }),
    );
    setEditingHolding(null);
  };

  // 添加投资组合
  const addPortfolio = (portfolioData: Partial<Portfolio>) => {
    const newPortfolio: Portfolio = {
      id: Date.now().toString(),
      name: portfolioData.name || "新投资组合",
      description: portfolioData.description || "",
      strategy: portfolioData.strategy || "",
      riskLevel: portfolioData.riskLevel || "moderate",
      currency: portfolioData.currency || "USD",
      holdings: [],
      isVisibleInComparison: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setPortfolios([...portfolios, newPortfolio]);
    setIsAddPortfolioOpen(false);
  };

  // 更新投资组合
  const updatePortfolio = (
    portfolioId: string,
    updates: Partial<Portfolio>,
  ) => {
    setPortfolios(
      portfolios.map((p) =>
        p.id === portfolioId ? { ...p, ...updates, updatedAt: new Date() } : p,
      ),
    );
    setEditingPortfolio(null);
  };

  // 基于真实投资记录生成图表数据
  const generateRealChartData = (items: (InvestmentHolding | Portfolio)[]) => {
    const allDates = new Set<string>();

    // 收集所有投资日期
    items.forEach((item) => {
      if ("records" in item) {
        // 这是一个holding
        item.records.forEach((record) => {
          allDates.add(record.date.toISOString().split("T")[0]);
        });
      } else {
        // 这是一个portfolio，需要获取其holdings的记录
        item.holdings.forEach((ph) => {
          const holding = holdings.find((h) => h.id === ph.holdingId);
          if (holding) {
            holding.records.forEach((record) => {
              allDates.add(record.date.toISOString().split("T")[0]);
            });
          }
        });
      }
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 为每个日期计算累积价值
    const chartData = sortedDates.map((dateStr) => {
      const date = new Date(dateStr);
      const dataPoint: any = {
        date: date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
      };

      items.forEach((item) => {
        if ("records" in item) {
          // Holdings对比
          const recordsUpToDate = item.records.filter(
            (r) => new Date(r.date) <= date,
          );

          const cumulativeInvestment = recordsUpToDate.reduce(
            (sum, r) => sum + r.amount,
            0,
          );

          const daysSinceStart =
            recordsUpToDate.length > 0
              ? Math.max(
                  1,
                  (date.getTime() -
                    new Date(recordsUpToDate[0].date).getTime()) /
                    (1000 * 60 * 60 * 24),
                )
              : 0;

          const growthFactor = Math.pow(1.08, daysSinceStart / 365);
          dataPoint[item.symbol || item.name] =
            cumulativeInvestment * growthFactor;
        } else {
          // Portfolio对比
          let portfolioValue = 0;
          item.holdings.forEach((ph) => {
            const holding = holdings.find((h) => h.id === ph.holdingId);
            if (holding) {
              const recordsUpToDate = holding.records.filter(
                (r) => new Date(r.date) <= date,
              );

              const cumulativeInvestment = recordsUpToDate.reduce(
                (sum, r) => sum + r.amount,
                0,
              );

              const daysSinceStart =
                recordsUpToDate.length > 0
                  ? Math.max(
                      1,
                      (date.getTime() -
                        new Date(recordsUpToDate[0].date).getTime()) /
                        (1000 * 60 * 60 * 24),
                    )
                  : 0;

              const growthFactor = Math.pow(1.08, daysSinceStart / 365);
              portfolioValue +=
                cumulativeInvestment *
                growthFactor *
                (ph.targetAllocation / 100);
            }
          });
          dataPoint[item.name] = portfolioValue;
        }
      });

      return dataPoint;
    });

    return chartData;
  };

  return (
    <section id="portfolio-system" className="bg-background py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto mb-16 max-w-2xl text-center">
          <Badge variant="secondary" className="mb-4">
            <Briefcase className="mr-2 h-3 w-3" />
            Investment Management System
          </Badge>

          <h2 className="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
            Manage Holdings & Compare Performance
          </h2>

          <p className="text-muted-foreground mt-4 text-lg">
            Track individual holdings or create portfolios. Compare performance
            with real data-driven charts and analytics.
          </p>
        </div>

        {/* View Mode Controls */}
        <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-4">
            {/* View Mode Tabs */}
            <div className="flex rounded-lg border">
              <Button
                variant={viewMode === "holdings" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("holdings")}
              >
                <Target className="mr-2 h-4 w-4" />
                Holdings
              </Button>
              <Button
                variant={viewMode === "portfolios" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("portfolios")}
              >
                <Briefcase className="mr-2 h-4 w-4" />
                Portfolios
              </Button>
              <Button
                variant={viewMode === "comparison" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("comparison")}
              >
                <LineChart className="mr-2 h-4 w-4" />
                Comparison
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button size="sm" onClick={() => setIsCreatingNewHolding(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Holding
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddPortfolioOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Portfolio
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <>
          {viewMode === "holdings" && (
            <HoldingsView
              holdings={holdings}
              onEditHolding={setManagingHolding}
              onDeleteHolding={(id) =>
                setHoldings((prev) => prev.filter((h) => h.id !== id))
              }
              onUpdateHolding={(id, updates) =>
                setHoldings((prev) =>
                  prev.map((h) => (h.id === id ? { ...h, ...updates } : h)),
                )
              }
            />
          )}

          {viewMode === "portfolios" && (
            <PortfoliosView
              portfolios={portfolios}
              holdings={holdings}
              onEditPortfolio={setEditingPortfolio}
              onDeletePortfolio={(id) =>
                setPortfolios((prev) => prev.filter((p) => p.id !== id))
              }
              onUpdatePortfolio={(id, updates) =>
                setPortfolios((prev) =>
                  prev.map((p) => (p.id === id ? { ...p, ...updates } : p)),
                )
              }
            />
          )}

          {viewMode === "comparison" && (
            <ComparisonView
              holdings={holdings}
              portfolios={portfolios}
              comparisonMode={comparisonMode}
              onComparisonModeChange={setComparisonMode}
              onToggleHoldingVisibility={(id) =>
                setHoldings((prev) =>
                  prev.map((h) =>
                    h.id === id
                      ? {
                          ...h,
                          isVisibleInComparison: !h.isVisibleInComparison,
                        }
                      : h,
                  ),
                )
              }
              onTogglePortfolioVisibility={(id) =>
                setPortfolios((prev) =>
                  prev.map((p) =>
                    p.id === id
                      ? {
                          ...p,
                          isVisibleInComparison: !p.isVisibleInComparison,
                        }
                      : p,
                  ),
                )
              }
              chartData={generateRealChartData(
                comparisonMode === "holdings"
                  ? holdings.filter((h) => h.isVisibleInComparison)
                  : portfolios.filter((p) => p.isVisibleInComparison),
              )}
            />
          )}
        </>

        {/* Integrated Holding Manager */}
        {(managingHolding || isCreatingNewHolding) && (
          <IntegratedHoldingManager
            holding={
              managingHolding || {
                id: "",
                name: "",
                symbol: "",
                type: "etf",
                currency: "USD",
                color: COLORS[holdings.length % COLORS.length],
                records: [],
                isVisibleInComparison: true,
                currentPrice: 0,
                lastUpdated: new Date(),
                totalInvested: 0,
                currentValue: 0,
                totalShares: 0,
                averageCost: 0,
                unrealizedGain: 0,
                unrealizedGainPercent: 0,
              }
            }
            onSave={(holding) => {
              if (isCreatingNewHolding) {
                // 创建新的holding
                const newHolding = {
                  ...holding,
                  id: Date.now().toString(),
                };
                setHoldings([...holdings, newHolding]);
                setIsCreatingNewHolding(false);
              } else if (managingHolding) {
                // 更新现有holding
                setHoldings(
                  holdings.map((h) =>
                    h.id === managingHolding.id ? holding : h,
                  ),
                );
                setManagingHolding(null);
              }
            }}
            onCancel={() => {
              setManagingHolding(null);
              setIsCreatingNewHolding(false);
            }}
            isNew={isCreatingNewHolding}
          />
        )}

        {editingPortfolio && (
          <Dialog
            open={!!editingPortfolio}
            onOpenChange={() => setEditingPortfolio(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Portfolio</DialogTitle>
              </DialogHeader>
              <PortfolioForm
                initialData={editingPortfolio}
                onSave={(updates) =>
                  updatePortfolio(editingPortfolio.id, updates)
                }
                availableHoldings={holdings}
              />
            </DialogContent>
          </Dialog>
        )}

        <Dialog open={isAddPortfolioOpen} onOpenChange={setIsAddPortfolioOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Portfolio</DialogTitle>
              <DialogDescription>
                Create a new investment portfolio. You can select holdings and
                set allocations.
              </DialogDescription>
            </DialogHeader>
            <PortfolioForm onSave={addPortfolio} availableHoldings={holdings} />
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
}
