const { drizzle } = require("drizzle-orm/postgres-js");
const postgres = require("postgres");
const { eq } = require("drizzle-orm");
const {
  pgTable,
  text,
  integer,
  timestamp,
  boolean,
  uuid,
} = require("drizzle-orm/pg-core");

// Define tables directly since we can't import from TypeScript
const users = pgTable("users", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  paymentProviderCustomerId: text("paymentProviderCustomerId").unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

const subscriptions = pgTable("subscriptions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("userId").notNull(),
  customerId: text("customerId").notNull(),
  subscriptionId: text("subscriptionId").notNull().unique(),
  productId: text("productId").notNull(),
  status: text("status").notNull(),
  currentPeriodStart: timestamp("currentPeriodStart"),
  currentPeriodEnd: timestamp("currentPeriodEnd"),
  canceledAt: timestamp("canceledAt"),
  createdAt: timestamp("createdAt").notNull().defaultNow(),
  updatedAt: timestamp("updatedAt").notNull().defaultNow(),
});

const payments = pgTable("payments", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("userId").notNull(),
  customerId: text("customerId").notNull(),
  subscriptionId: text("subscriptionId"),
  productId: text("productId").notNull(),
  paymentId: text("paymentId").notNull().unique(),
  amount: integer("amount").notNull(),
  currency: text("currency").notNull().default("usd"),
  status: text("status").notNull(),
  paymentType: text("paymentType").notNull(),
  createdAt: timestamp("createdAt").notNull().defaultNow(),
  updatedAt: timestamp("updatedAt").notNull().defaultNow(),
});

const webhookEvents = pgTable("webhook_events", {
  id: uuid("id").primaryKey().defaultRandom(),
  eventId: text("eventId").notNull().unique(),
  eventType: text("eventType").notNull(),
  provider: text("provider").notNull().default("creem"),
  processed: boolean("processed").notNull().default(true),
  processedAt: timestamp("processedAt").notNull().defaultNow(),
  payload: text("payload"),
  createdAt: timestamp("createdAt").notNull().defaultNow(),
});

// Load environment variables
require("dotenv").config();

// Database connection
const connectionString = process.env.DATABASE_URL;
const sql = postgres(connectionString);
const db = drizzle(sql);

async function debugPaymentIssue() {
  try {
    console.log("🔍 Debugging payment issue...\n");

    // Find user by email
    const userEmail = "<EMAIL>";
    console.log(`Looking for user with email: ${userEmail}`);

    const user = await db
      .select()
      .from(users)
      .where(eq(users.email, userEmail))
      .limit(1);

    if (user.length === 0) {
      console.log("❌ User not found!");
      return;
    }

    const userData = user[0];
    console.log("✅ User found:", {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      paymentProviderCustomerId: userData.paymentProviderCustomerId,
    });

    console.log("\n📋 Checking subscriptions...");
    const userSubscriptions = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, userData.id));
    console.log(`Found ${userSubscriptions.length} subscription(s):`);
    userSubscriptions.forEach((sub, index) => {
      console.log(
        `  ${index + 1}. ID: ${sub.subscriptionId}, Status: ${sub.status}, Product: ${sub.productId}`,
      );
    });

    console.log("\n💳 Checking payments...");
    const userPayments = await db
      .select()
      .from(payments)
      .where(eq(payments.userId, userData.id));
    console.log(`Found ${userPayments.length} payment(s):`);
    userPayments.forEach((payment, index) => {
      console.log(
        `  ${index + 1}. ID: ${payment.paymentId}, Amount: ${payment.amount}, Status: ${payment.status}, Type: ${payment.paymentType}`,
      );
    });

    console.log("\n🔗 Checking webhook events...");
    const recentWebhooks = await db.select().from(webhookEvents).limit(10);
    console.log(`Found ${recentWebhooks.length} recent webhook event(s):`);
    recentWebhooks.forEach((webhook, index) => {
      console.log(
        `  ${index + 1}. Event: ${webhook.eventType}, Provider: ${webhook.provider}, Processed: ${webhook.processed}`,
      );
    });
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await sql.end();
  }
}

debugPaymentIssue();
