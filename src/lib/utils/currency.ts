import { SupportedCurrency, CurrencyInfo } from "../calculations/types";

// 货币配置信息
export const CURRENCY_CONFIG: Record<SupportedCurrency, CurrencyInfo> = {
  USD: {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    locale: "en-US",
  },
  EUR: {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    locale: "de-DE",
  },
  GBP: {
    code: "GBP",
    symbol: "£",
    name: "British Pound",
    locale: "en-GB",
  },
  JPY: {
    code: "JPY",
    symbol: "¥",
    name: "Japanese Yen",
    locale: "ja-JP",
  },
  CNY: {
    code: "CNY",
    symbol: "¥",
    name: "Chinese Yuan",
    locale: "zh-CN",
  },
  KRW: {
    code: "KRW",
    symbol: "₩",
    name: "Korean Won",
    locale: "ko-KR",
  },
  CAD: {
    code: "CAD",
    symbol: "C$",
    name: "Canadian Dollar",
    locale: "en-CA",
  },
  AUD: {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    locale: "en-AU",
  },
};

// 货币格式化选项
export interface CurrencyFormatOptions {
  compact?: boolean;
  showSymbol?: boolean;
  precision?: number;
  showCode?: boolean;
}

// 货币工具类
export class CurrencyUtils {
  // 格式化货币金额
  static format(
    amount: number,
    currency: SupportedCurrency,
    options: CurrencyFormatOptions = {},
  ): string {
    const {
      compact = false,
      showSymbol = true,
      precision = 2,
      showCode = false,
    } = options;

    const config = CURRENCY_CONFIG[currency];

    if (!config) {
      throw new Error(`Unsupported currency: ${currency}`);
    }

    // 处理紧凑格式
    if (compact && Math.abs(amount) >= 1000) {
      return CurrencyUtils.formatCompact(amount, currency, showSymbol);
    }

    // 标准格式化
    const formatter = new Intl.NumberFormat(config.locale, {
      style: showSymbol ? "currency" : "decimal",
      currency: currency,
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    let formatted = formatter.format(amount);

    // 添加货币代码
    if (showCode && !showSymbol) {
      formatted = `${formatted} ${currency}`;
    }

    return formatted;
  }

  // 紧凑格式化（如 $1.2M, $45.3K）
  private static formatCompact(
    amount: number,
    currency: SupportedCurrency,
    showSymbol: boolean,
  ): string {
    const config = CURRENCY_CONFIG[currency];
    const absAmount = Math.abs(amount);

    let value: number;
    let suffix: string;

    if (absAmount >= 1e9) {
      value = amount / 1e9;
      suffix = "B";
    } else if (absAmount >= 1e6) {
      value = amount / 1e6;
      suffix = "M";
    } else if (absAmount >= 1e3) {
      value = amount / 1e3;
      suffix = "K";
    } else {
      return CurrencyUtils.format(amount, currency, {
        showSymbol,
        precision: 2,
      });
    }

    const formatted = value.toFixed(1);
    const symbol = showSymbol ? config.symbol : "";

    return `${symbol}${formatted}${suffix}`;
  }

  // 解析货币字符串为数字
  static parse(value: string, currency: SupportedCurrency): number {
    const config = CURRENCY_CONFIG[currency];

    // 移除货币符号和格式化字符
    const cleanValue = value
      .replace(new RegExp(`\\${config.symbol}`, "g"), "")
      .replace(/[,\s]/g, "")
      .replace(currency, "")
      .trim();

    // 处理紧凑格式后缀
    const lastChar = cleanValue.slice(-1).toUpperCase();
    if (["K", "M", "B"].includes(lastChar)) {
      const numValue = parseFloat(cleanValue.slice(0, -1));
      switch (lastChar) {
        case "K":
          return numValue * 1000;
        case "M":
          return numValue * 1000000;
        case "B":
          return numValue * 1000000000;
      }
    }

    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }

  // 验证货币代码
  static isValidCurrency(currency: string): currency is SupportedCurrency {
    return currency in CURRENCY_CONFIG;
  }

  // 获取货币信息
  static getCurrencyInfo(currency: SupportedCurrency): CurrencyInfo {
    return CURRENCY_CONFIG[currency];
  }

  // 获取所有支持的货币
  static getAllCurrencies(): CurrencyInfo[] {
    return Object.values(CURRENCY_CONFIG);
  }

  // 获取货币符号
  static getSymbol(currency: SupportedCurrency): string {
    return CURRENCY_CONFIG[currency]?.symbol || currency;
  }

  // 获取货币名称
  static getName(currency: SupportedCurrency): string {
    return CURRENCY_CONFIG[currency]?.name || currency;
  }

  // 获取本地化设置
  static getLocale(currency: SupportedCurrency): string {
    return CURRENCY_CONFIG[currency]?.locale || "en-US";
  }

  // 格式化为输入字段友好的格式
  static formatForInput(amount: number, currency: SupportedCurrency): string {
    const config = CURRENCY_CONFIG[currency];

    // 对于某些货币（如日元、韩元），不显示小数位
    const precision = ["JPY", "KRW"].includes(currency) ? 0 : 2;

    return new Intl.NumberFormat(config.locale, {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(amount);
  }

  // 验证金额格式
  static validateAmount(value: string, currency: SupportedCurrency): boolean {
    try {
      const parsed = CurrencyUtils.parse(value, currency);
      return !isNaN(parsed) && parsed >= 0;
    } catch {
      return false;
    }
  }

  // 获取货币的小数位数
  static getDecimalPlaces(currency: SupportedCurrency): number {
    // 日元和韩元通常不使用小数位
    return ["JPY", "KRW"].includes(currency) ? 0 : 2;
  }

  // 格式化百分比
  static formatPercentage(value: number, precision: number = 2): string {
    return new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value / 100);
  }

  // 格式化数字（无货币符号）
  static formatNumber(value: number, precision: number = 0): string {
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value);
  }

  // 获取默认货币（基于用户地理位置或偏好）
  static getDefaultCurrency(): SupportedCurrency {
    // 尝试从浏览器获取地理位置信息
    try {
      const locale = navigator.language || "en-US";

      // 根据地区代码推断货币
      if (locale.includes("US")) return "USD";
      if (locale.includes("GB")) return "GBP";
      if (
        locale.includes("DE") ||
        locale.includes("FR") ||
        locale.includes("IT")
      )
        return "EUR";
      if (locale.includes("JP")) return "JPY";
      if (locale.includes("CN")) return "CNY";
      if (locale.includes("KR")) return "KRW";
      if (locale.includes("CA")) return "CAD";
      if (locale.includes("AU")) return "AUD";

      return "USD"; // 默认美元
    } catch {
      return "USD";
    }
  }
}

// 导出常用函数
export const formatCurrency = CurrencyUtils.format;
export const parseCurrency = CurrencyUtils.parse;
export const getCurrencySymbol = CurrencyUtils.getSymbol;
export const getCurrencyName = CurrencyUtils.getName;
export const getAllCurrencies = CurrencyUtils.getAllCurrencies;
export const getDefaultCurrency = CurrencyUtils.getDefaultCurrency;
