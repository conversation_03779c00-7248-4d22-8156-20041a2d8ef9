"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DollarSign,
  TrendingUp,
  Target,
  Calendar,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  PieChart,
  Plus,
  Briefcase,
  ChevronDown,
  ChevronRight,
  Settings,
  LineChart,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON> as RechartsL<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  Toolt<PERSON>,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { PercentageInputField } from "@/components/calculator/core/InputField";
import { RecordForm } from "./portfolio-forms-v2";

// 类型定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  currency: string;
  color: string;
  records: InvestmentRecord[];
  isVisibleInComparison: boolean;
  totalInvested: number;
  currentValue: number;
  totalShares: number;
  averageCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
}

interface PortfolioHolding {
  holdingId: string;
  targetAllocation: number;
  isVisible: boolean;
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: string;
  holdings: PortfolioHolding[];
  isVisibleInComparison: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Holdings View - 核心投资标的管理
export function HoldingsView({
  holdings,
  onEditHolding,
  onDeleteHolding,
  onUpdateHolding,
}: {
  holdings: InvestmentHolding[];
  onEditHolding: (holding: InvestmentHolding) => void;
  onDeleteHolding: (holdingId: string) => void;
  onUpdateHolding: (
    holdingId: string,
    updates: Partial<InvestmentHolding>,
  ) => void;
}) {
  const [expandedHoldings, setExpandedHoldings] = useState<Set<string>>(
    new Set(),
  );
  const [editingRecord, setEditingRecord] = useState<{
    holdingId: string;
    record: InvestmentRecord;
  } | null>(null);
  const [addingRecordToHolding, setAddingRecordToHolding] = useState<
    string | null
  >(null);

  const toggleHoldingExpansion = (holdingId: string) => {
    const newExpanded = new Set(expandedHoldings);
    if (newExpanded.has(holdingId)) {
      newExpanded.delete(holdingId);
    } else {
      newExpanded.add(holdingId);
    }
    setExpandedHoldings(newExpanded);
  };

  const handleAddRecord = (
    holdingId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    const newRecord: InvestmentRecord = {
      id: Date.now().toString(),
      date: recordData.date || new Date(),
      amount: recordData.amount || 0,
      price: recordData.price,
      shares: recordData.shares,
      fees: recordData.fees,
      note: recordData.note,
      type: recordData.type || "buy",
    };

    onUpdateHolding(holdingId, {
      records: [
        ...(holdings.find((h) => h.id === holdingId)?.records || []),
        newRecord,
      ],
    });
    setAddingRecordToHolding(null);
  };

  const handleUpdateRecord = (recordData: Partial<InvestmentRecord>) => {
    if (editingRecord) {
      const holding = holdings.find((h) => h.id === editingRecord.holdingId);
      if (holding) {
        onUpdateHolding(editingRecord.holdingId, {
          records: holding.records.map((r) =>
            r.id === editingRecord.record.id ? { ...r, ...recordData } : r,
          ),
        });
      }
      setEditingRecord(null);
    }
  };

  const handleDeleteRecord = (holdingId: string, recordId: string) => {
    const holding = holdings.find((h) => h.id === holdingId);
    if (holding) {
      onUpdateHolding(holdingId, {
        records: holding.records.filter((r) => r.id !== recordId),
      });
    }
  };

  // 计算总体统计
  const totalValue = holdings.reduce((sum, h) => sum + h.currentValue, 0);
  const totalInvested = holdings.reduce((sum, h) => sum + h.totalInvested, 0);
  const totalGain = totalValue - totalInvested;
  const totalGainPercent =
    totalInvested > 0 ? (totalGain / totalInvested) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Holdings Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Investment Holdings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="text-center">
              <DollarSign className="mx-auto mb-2 h-8 w-8 text-blue-500" />
              <p className="text-muted-foreground text-sm">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(totalValue, "USD")}
              </p>
            </div>
            <div className="text-center">
              <TrendingUp className="mx-auto mb-2 h-8 w-8 text-green-500" />
              <p className="text-muted-foreground text-sm">Total Gain</p>
              <p
                className={`text-2xl font-bold ${totalGain >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {formatCurrency(totalGain, "USD")}
              </p>
            </div>
            <div className="text-center">
              <BarChart3 className="mx-auto mb-2 h-8 w-8 text-purple-500" />
              <p className="text-muted-foreground text-sm">Return %</p>
              <p
                className={`text-2xl font-bold ${totalGainPercent >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {CurrencyUtils.formatPercentage(totalGainPercent)}
              </p>
            </div>
            <div className="text-center">
              <Target className="mx-auto mb-2 h-8 w-8 text-orange-500" />
              <p className="text-muted-foreground text-sm">Holdings</p>
              <p className="text-2xl font-bold">{holdings.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Holdings List with Expandable Records */}
      <div className="space-y-4">
        {holdings.map((holding) => {
          const isExpanded = expandedHoldings.has(holding.id);

          return (
            <Card key={holding.id} className="overflow-hidden">
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <CardHeader
                    className="hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => toggleHoldingExpansion(holding.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <div
                            className="h-4 w-4 rounded-full"
                            style={{ backgroundColor: holding.color }}
                          />
                        </div>

                        <div>
                          <CardTitle className="text-lg">
                            {holding.name}
                          </CardTitle>
                          <div className="mt-1 flex items-center gap-4">
                            {holding.symbol && (
                              <Badge variant="outline">{holding.symbol}</Badge>
                            )}
                            <Badge variant="secondary">
                              {holding.type.replace("_", " ").toUpperCase()}
                            </Badge>
                            <span className="text-muted-foreground text-sm">
                              {holding.records.length} records
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        {/* Key Metrics */}
                        <div className="text-right">
                          <p className="font-semibold">
                            {formatCurrency(
                              holding.currentValue,
                              holding.currency,
                            )}
                          </p>
                          <p
                            className={`text-sm ${
                              holding.unrealizedGain >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              holding.unrealizedGain,
                              holding.currency,
                            )}
                            (
                            {CurrencyUtils.formatPercentage(
                              holding.unrealizedGainPercent,
                            )}
                            )
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div
                          className="flex items-center gap-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setAddingRecordToHolding(holding.id)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditHolding(holding)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteHolding(holding.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    {/* Holding Details */}
                    <div className="bg-muted/30 mb-6 grid grid-cols-2 gap-4 rounded-lg p-4 md:grid-cols-6">
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Current Price
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.currentPrice,
                            holding.currency,
                          )}
                        </p>
                        <p className="text-muted-foreground text-xs">
                          Updated:{" "}
                          {new Date(holding.lastUpdated).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Shares
                        </p>
                        <p className="font-semibold">
                          {holding.totalShares.toFixed(3)}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Average Cost
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.averageCost,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Invested
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.totalInvested,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Current Value
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.currentValue,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Unrealized P&L
                        </p>
                        <p
                          className={`font-semibold ${
                            holding.unrealizedGain >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {formatCurrency(
                            holding.unrealizedGain,
                            holding.currency,
                          )}
                        </p>
                        <p
                          className={`text-xs ${
                            holding.unrealizedGainPercent >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {CurrencyUtils.formatPercentage(
                            holding.unrealizedGainPercent,
                          )}
                        </p>
                      </div>
                    </div>

                    {/* Investment Records */}
                    <div>
                      <div className="mb-4 flex items-center justify-between">
                        <h4 className="font-medium">Investment Records</h4>
                        <Button
                          size="sm"
                          onClick={() => setAddingRecordToHolding(holding.id)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Record
                        </Button>
                      </div>

                      {holding.records.length === 0 ? (
                        <div className="bg-muted/20 rounded-lg py-8 text-center">
                          <Calendar className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                          <p className="text-muted-foreground">
                            No investment records yet
                          </p>
                          <p className="text-muted-foreground text-sm">
                            Add your first investment record
                          </p>
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Price</TableHead>
                              <TableHead>Shares</TableHead>
                              <TableHead>Fees</TableHead>
                              <TableHead>Note</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {holding.records.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell>
                                  {new Date(record.date).toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={
                                      record.type === "buy"
                                        ? "default"
                                        : record.type === "sell"
                                          ? "destructive"
                                          : "secondary"
                                    }
                                  >
                                    {record.type.toUpperCase()}
                                  </Badge>
                                </TableCell>
                                <TableCell className="font-medium">
                                  {formatCurrency(
                                    record.amount,
                                    holding.currency,
                                  )}
                                </TableCell>
                                <TableCell>
                                  {record.price
                                    ? formatCurrency(
                                        record.price,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.shares
                                    ? record.shares.toFixed(4)
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.fees
                                    ? formatCurrency(
                                        record.fees,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell className="max-w-32 truncate">
                                  {record.note || "-"}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        setEditingRecord({
                                          holdingId: holding.id,
                                          record,
                                        })
                                      }
                                    >
                                      <Edit2 className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        handleDeleteRecord(
                                          holding.id,
                                          record.id,
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      {/* Add Record Dialog */}
      {addingRecordToHolding && (
        <Dialog
          open={!!addingRecordToHolding}
          onOpenChange={() => setAddingRecordToHolding(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Investment Record</DialogTitle>
              <DialogDescription>
                Add a new investment record to{" "}
                {holdings.find((h) => h.id === addingRecordToHolding)?.name}
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              onSave={handleAddRecord.bind(null, addingRecordToHolding)}
              currency={
                holdings.find((h) => h.id === addingRecordToHolding)
                  ?.currency || "USD"
              }
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Record Dialog */}
      {editingRecord && (
        <Dialog
          open={!!editingRecord}
          onOpenChange={() => setEditingRecord(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Investment Record</DialogTitle>
              <DialogDescription>
                Edit investment record for{" "}
                {holdings.find((h) => h.id === editingRecord.holdingId)?.name}
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              initialData={editingRecord.record}
              onSave={handleUpdateRecord}
              currency={
                holdings.find((h) => h.id === editingRecord.holdingId)
                  ?.currency || "USD"
              }
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Portfolios View - 投资组合管理
export function PortfoliosView({
  portfolios,
  holdings,
  onEditPortfolio,
  onDeletePortfolio,
  onUpdatePortfolio,
}: {
  portfolios: Portfolio[];
  holdings: InvestmentHolding[];
  onEditPortfolio: (portfolio: Portfolio) => void;
  onDeletePortfolio: (portfolioId: string) => void;
  onUpdatePortfolio: (portfolioId: string, updates: Partial<Portfolio>) => void;
}) {
  // 获取组合的完整标的信息
  const getPortfolioHoldingsWithData = (portfolio: Portfolio) => {
    return portfolio.holdings
      .map((ph) => {
        const holding = holdings.find((h) => h.id === ph.holdingId);
        return {
          ...ph,
          ...holding,
          targetAllocation: ph.targetAllocation,
          isVisible: ph.isVisible,
        };
      })
      .filter((h) => h.name);
  };

  // 计算组合统计
  const calculatePortfolioStats = (portfolio: Portfolio) => {
    const holdingsWithData = getPortfolioHoldingsWithData(portfolio);
    const totalValue = holdingsWithData.reduce(
      (sum, h) => sum + (h.currentValue || 0),
      0,
    );
    const totalInvested = holdingsWithData.reduce(
      (sum, h) => sum + (h.totalInvested || 0),
      0,
    );
    const totalGain = totalValue - totalInvested;
    const totalGainPercent =
      totalInvested > 0 ? (totalGain / totalInvested) * 100 : 0;

    return {
      totalValue,
      totalInvested,
      totalGain,
      totalGainPercent,
      holdingsWithData,
    };
  };

  return (
    <div className="space-y-6">
      {/* Portfolios Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            Investment Portfolios
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Portfolios</p>
              <p className="text-2xl font-bold">{portfolios.length}</p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(
                  portfolios.reduce(
                    (sum, p) => sum + calculatePortfolioStats(p).totalValue,
                    0,
                  ),
                  "USD",
                )}
              </p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">
                Available Holdings
              </p>
              <p className="text-2xl font-bold">{holdings.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Portfolios List */}
      <div className="grid gap-6 md:grid-cols-2">
        {portfolios.map((portfolio) => {
          const stats = calculatePortfolioStats(portfolio);

          return (
            <Card key={portfolio.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Briefcase className="h-5 w-5" />
                      {portfolio.name}
                    </CardTitle>
                    <p className="text-muted-foreground mt-1">
                      {portfolio.description}
                    </p>
                    <div className="mt-2 flex items-center gap-4">
                      <Badge
                        variant={
                          portfolio.riskLevel === "conservative"
                            ? "secondary"
                            : portfolio.riskLevel === "moderate"
                              ? "default"
                              : "destructive"
                        }
                      >
                        {portfolio.riskLevel}
                      </Badge>
                      <span className="text-muted-foreground text-sm">
                        {portfolio.strategy}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditPortfolio(portfolio)}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeletePortfolio(portfolio.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Portfolio Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-muted-foreground text-sm">
                        Total Value
                      </p>
                      <p className="text-lg font-semibold">
                        {formatCurrency(stats.totalValue, portfolio.currency)}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground text-sm">
                        Total Gain
                      </p>
                      <p
                        className={`text-lg font-semibold ${
                          stats.totalGain >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {formatCurrency(stats.totalGain, portfolio.currency)}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground text-sm">Return %</p>
                      <p
                        className={`text-lg font-semibold ${
                          stats.totalGainPercent >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {CurrencyUtils.formatPercentage(stats.totalGainPercent)}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground text-sm">Holdings</p>
                      <p className="text-lg font-semibold">
                        {portfolio.holdings.length}
                      </p>
                    </div>
                  </div>

                  {/* Holdings List */}
                  <div>
                    <h5 className="mb-2 font-medium">Holdings</h5>
                    {stats.holdingsWithData.length === 0 ? (
                      <p className="text-muted-foreground text-sm">
                        No holdings selected
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {stats.holdingsWithData.map((holding) => {
                          const actualAllocation =
                            stats.totalValue > 0
                              ? ((holding.currentValue || 0) /
                                  stats.totalValue) *
                                100
                              : 0;

                          return (
                            <div
                              key={holding.holdingId}
                              className="flex items-center justify-between text-sm"
                            >
                              <div className="flex items-center gap-2">
                                <div
                                  className="h-3 w-3 rounded-full"
                                  style={{ backgroundColor: holding.color }}
                                />
                                <span>{holding.symbol || holding.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span>{actualAllocation.toFixed(1)}%</span>
                                <span className="text-muted-foreground">
                                  vs
                                </span>
                                <span>{holding.targetAllocation}%</span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>

                  {/* Allocation Chart */}
                  {stats.holdingsWithData.length > 0 && (
                    <div className="h-32">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={stats.holdingsWithData.map((h) => ({
                              name: h.symbol || h.name,
                              value: h.currentValue || 0,
                              color: h.color,
                            }))}
                            cx="50%"
                            cy="50%"
                            outerRadius={50}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {stats.holdingsWithData.map((holding, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={holding.color}
                              />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value: number) => [
                              formatCurrency(value, portfolio.currency),
                              "Value",
                            ]}
                          />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

// Comparison View - 灵活的对比功能
export function ComparisonView({
  holdings,
  portfolios,
  comparisonMode,
  onComparisonModeChange,
  onToggleHoldingVisibility,
  onTogglePortfolioVisibility,
  chartData,
}: {
  holdings: InvestmentHolding[];
  portfolios: Portfolio[];
  comparisonMode: "holdings" | "portfolios";
  onComparisonModeChange: (mode: "holdings" | "portfolios") => void;
  onToggleHoldingVisibility: (holdingId: string) => void;
  onTogglePortfolioVisibility: (portfolioId: string) => void;
  chartData: any[];
}) {
  const visibleHoldings = holdings.filter((h) => h.isVisibleInComparison);
  const visiblePortfolios = portfolios.filter((p) => p.isVisibleInComparison);

  // 准备饼图数据
  const preparePieData = () => {
    if (comparisonMode === "holdings") {
      return visibleHoldings.map((h) => ({
        name: h.symbol || h.name,
        value: h.currentValue,
        color: h.color,
        type: h.type,
      }));
    } else {
      return visiblePortfolios.map((p) => {
        const totalValue = p.holdings.reduce((sum, ph) => {
          const holding = holdings.find((h) => h.id === ph.holdingId);
          return (
            sum +
            (holding ? holding.currentValue * (ph.targetAllocation / 100) : 0)
          );
        }, 0);
        return {
          name: p.name,
          value: totalValue,
          color: `hsl(${Math.random() * 360}, 70%, 50%)`, // 动态颜色
          riskLevel: p.riskLevel,
        };
      });
    }
  };

  const pieData = preparePieData();

  return (
    <div className="space-y-6">
      {/* Comparison Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LineChart className="h-5 w-5" />
            Performance Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex rounded-lg border">
                <Button
                  variant={comparisonMode === "holdings" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onComparisonModeChange("holdings")}
                >
                  Compare Holdings
                </Button>
                <Button
                  variant={
                    comparisonMode === "portfolios" ? "default" : "ghost"
                  }
                  size="sm"
                  onClick={() => onComparisonModeChange("portfolios")}
                >
                  Compare Portfolios
                </Button>
              </div>
            </div>

            <div className="text-muted-foreground text-sm">
              {comparisonMode === "holdings"
                ? `${visibleHoldings.length} holdings visible`
                : `${visiblePortfolios.length} portfolios visible`}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Performance Line Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Performance Over Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            {chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={400}>
                <RechartsLineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip
                    formatter={(value: number) => [
                      formatCurrency(value, "USD"),
                      "Value",
                    ]}
                  />
                  <Legend />
                  {comparisonMode === "holdings"
                    ? visibleHoldings.map((holding) => (
                        <Line
                          key={holding.id}
                          type="monotone"
                          dataKey={holding.symbol || holding.name}
                          stroke={holding.color}
                          strokeWidth={2}
                        />
                      ))
                    : visiblePortfolios.map((portfolio, index) => (
                        <Line
                          key={portfolio.id}
                          type="monotone"
                          dataKey={portfolio.name}
                          stroke={`hsl(${index * 60}, 70%, 50%)`}
                          strokeWidth={2}
                        />
                      ))}
                </RechartsLineChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-muted-foreground flex h-[400px] items-center justify-center">
                <div className="text-center">
                  <Calendar className="mx-auto mb-4 h-12 w-12" />
                  <p>No data to compare</p>
                  <p className="text-sm">
                    {comparisonMode === "holdings"
                      ? "Add investment records to holdings to see comparison"
                      : "Create portfolios with holdings to see comparison"}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Allocation/Type Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {comparisonMode === "holdings"
                ? "Asset Type Distribution"
                : "Portfolio Allocation"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {pieData.length > 0 ? (
              <ResponsiveContainer width="100%" height={400}>
                <RechartsPieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(1)}%`
                    }
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [
                      formatCurrency(value, "USD"),
                      "Value",
                    ]}
                  />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-muted-foreground flex h-[400px] items-center justify-center">
                <div className="text-center">
                  <PieChart className="mx-auto mb-4 h-12 w-12" />
                  <p>No data to display</p>
                  <p className="text-sm">
                    {comparisonMode === "holdings"
                      ? "No holdings visible for comparison"
                      : "No portfolios visible for comparison"}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Visibility Controls */}
      <Card>
        <CardHeader>
          <CardTitle>
            {comparisonMode === "holdings"
              ? "Holdings Visibility"
              : "Portfolios Visibility"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {comparisonMode === "holdings" ? (
            <div className="grid gap-4 md:grid-cols-2">
              {holdings.map((holding) => (
                <div
                  key={holding.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="h-4 w-4 rounded-full"
                      style={{ backgroundColor: holding.color }}
                    />
                    <div>
                      <p className="font-medium">{holding.name}</p>
                      <p className="text-muted-foreground text-sm">
                        {holding.symbol} •{" "}
                        {formatCurrency(holding.currentValue, holding.currency)}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant={
                      holding.isVisibleInComparison ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onToggleHoldingVisibility(holding.id)}
                  >
                    {holding.isVisibleInComparison ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2">
              {portfolios.map((portfolio) => (
                <div
                  key={portfolio.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex items-center gap-3">
                    <Briefcase className="h-4 w-4" />
                    <div>
                      <p className="font-medium">{portfolio.name}</p>
                      <p className="text-muted-foreground text-sm">
                        {portfolio.holdings.length} holdings •{" "}
                        {portfolio.riskLevel}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant={
                      portfolio.isVisibleInComparison ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onTogglePortfolioVisibility(portfolio.id)}
                  >
                    {portfolio.isVisibleInComparison ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Comparison Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Comparison Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Total Invested</TableHead>
                <TableHead>Gain/Loss</TableHead>
                <TableHead>Return %</TableHead>
                {comparisonMode === "holdings" && <TableHead>Type</TableHead>}
                {comparisonMode === "portfolios" && (
                  <TableHead>Risk Level</TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {comparisonMode === "holdings"
                ? visibleHoldings.map((holding) => (
                    <TableRow key={holding.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ backgroundColor: holding.color }}
                          />
                          <div>
                            <p className="font-medium">{holding.name}</p>
                            {holding.symbol && (
                              <p className="text-muted-foreground text-sm">
                                {holding.symbol}
                              </p>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatCurrency(holding.currentValue, holding.currency)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(
                          holding.totalInvested,
                          holding.currency,
                        )}
                      </TableCell>
                      <TableCell>
                        <span
                          className={
                            holding.unrealizedGain >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }
                        >
                          {formatCurrency(
                            holding.unrealizedGain,
                            holding.currency,
                          )}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={
                            holding.unrealizedGainPercent >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }
                        >
                          {CurrencyUtils.formatPercentage(
                            holding.unrealizedGainPercent,
                          )}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {holding.type.replace("_", " ").toUpperCase()}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                : visiblePortfolios.map((portfolio) => {
                    const totalValue = portfolio.holdings.reduce((sum, ph) => {
                      const holding = holdings.find(
                        (h) => h.id === ph.holdingId,
                      );
                      return (
                        sum +
                        (holding
                          ? holding.currentValue * (ph.targetAllocation / 100)
                          : 0)
                      );
                    }, 0);
                    const totalInvested = portfolio.holdings.reduce(
                      (sum, ph) => {
                        const holding = holdings.find(
                          (h) => h.id === ph.holdingId,
                        );
                        return (
                          sum +
                          (holding
                            ? holding.totalInvested *
                              (ph.targetAllocation / 100)
                            : 0)
                        );
                      },
                      0,
                    );
                    const totalGain = totalValue - totalInvested;
                    const totalGainPercent =
                      totalInvested > 0 ? (totalGain / totalInvested) * 100 : 0;

                    return (
                      <TableRow key={portfolio.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Briefcase className="h-4 w-4" />
                            <div>
                              <p className="font-medium">{portfolio.name}</p>
                              <p className="text-muted-foreground text-sm">
                                {portfolio.description}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(totalValue, portfolio.currency)}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(totalInvested, portfolio.currency)}
                        </TableCell>
                        <TableCell>
                          <span
                            className={
                              totalGain >= 0 ? "text-green-600" : "text-red-600"
                            }
                          >
                            {formatCurrency(totalGain, portfolio.currency)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span
                            className={
                              totalGainPercent >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {CurrencyUtils.formatPercentage(totalGainPercent)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              portfolio.riskLevel === "conservative"
                                ? "secondary"
                                : portfolio.riskLevel === "moderate"
                                  ? "default"
                                  : "destructive"
                            }
                          >
                            {portfolio.riskLevel}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
