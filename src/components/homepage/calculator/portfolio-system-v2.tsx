"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Calculator,
  TrendingUp,
  DollarSign,
  PieChart,
  Plus,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  Sparkles,
  ArrowRight,
  BarChart3,
  Calendar,
  Target,
  Briefcase,
  Settings,
} from "lucide-react";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { PortfolioForm, HoldingForm } from "./portfolio-forms-v2";
import { OverviewView, HoldingsView } from "./portfolio-views-v3";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 正确的数据结构定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface InvestmentHolding {
  id: string;
  name: string; // 例如: "Vanguard S&P 500 ETF (VOO)"
  symbol?: string; // 例如: "VOO"
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  investmentMethod: "lump_sum" | "dca" | "mixed";
  currency: SupportedCurrency;
  targetAllocation: number; // 目标配置百分比
  currentValue: number;
  totalInvested: number;
  totalShares: number;
  averageCost: number;
  isVisible: boolean;
  color: string;
  records: InvestmentRecord[];
  // 计算结果
  unrealizedGain: number;
  unrealizedGainPercent: number;
  realizedGain: number;
}

interface Portfolio {
  id: string;
  name: string; // 例如: "土豆沙发投资组合"
  description: string;
  strategy: string; // 例如: "被动指数投资"
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: SupportedCurrency;
  holdings: InvestmentHolding[];
  totalValue: number;
  totalInvested: number;
  totalGain: number;
  totalGainPercent: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// 移除模板，用户直接创建空白组合

// 预定义颜色
const HOLDING_COLORS = [
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#ec4899",
  "#6366f1",
];

// 免费版限制
const FREE_TIER_LIMITS = {
  maxPortfolios: 1,
  maxHoldingsPerPortfolio: 3,
  maxRecordsPerHolding: 10,
};

export function PortfolioSystemV2() {
  // 状态管理
  const [portfolios, setPortfolios] = useState<Portfolio[]>([
    {
      id: "1",
      name: "我的土豆沙发组合",
      description: "简单的被动投资策略",
      strategy: "60% 股票 + 40% 债券",
      riskLevel: "moderate",
      currency: "USD",
      holdings: [
        {
          id: "h1",
          name: "Vanguard S&P 500 ETF",
          symbol: "VOO",
          type: "etf",
          investmentMethod: "dca",
          currency: "USD",
          targetAllocation: 60,
          currentValue: 12500,
          totalInvested: 10000,
          totalShares: 25.5,
          averageCost: 392.16,
          isVisible: true,
          color: HOLDING_COLORS[0],
          unrealizedGain: 2500,
          unrealizedGainPercent: 25,
          realizedGain: 0,
          records: [
            {
              id: "r1",
              date: new Date("2024-01-15"),
              amount: 5000,
              price: 400,
              shares: 12.5,
              fees: 0,
              note: "初始投资",
              type: "buy",
            },
            {
              id: "r2",
              date: new Date("2024-02-15"),
              amount: 2000,
              price: 410,
              shares: 4.88,
              fees: 0,
              note: "定期投资",
              type: "buy",
            },
            {
              id: "r3",
              date: new Date("2024-03-15"),
              amount: 3000,
              price: 380,
              shares: 7.89,
              fees: 0,
              note: "逢低加仓",
              type: "buy",
            },
          ],
        },
        {
          id: "h2",
          name: "Vanguard Total Bond Market ETF",
          symbol: "BND",
          type: "etf",
          investmentMethod: "lump_sum",
          currency: "USD",
          targetAllocation: 40,
          currentValue: 8200,
          totalInvested: 8000,
          totalShares: 100,
          averageCost: 80,
          isVisible: true,
          color: HOLDING_COLORS[1],
          unrealizedGain: 200,
          unrealizedGainPercent: 2.5,
          realizedGain: 0,
          records: [
            {
              id: "r4",
              date: new Date("2024-01-15"),
              amount: 8000,
              price: 80,
              shares: 100,
              fees: 0,
              note: "一次性投资债券",
              type: "buy",
            },
          ],
        },
      ],
      totalValue: 20700,
      totalInvested: 18000,
      totalGain: 2700,
      totalGainPercent: 15,
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date(),
      isActive: true,
    },
  ]);

  const [activePortfolio, setActivePortfolio] = useState<string>("1");
  const [viewMode, setViewMode] = useState<"overview" | "holdings">("overview");

  // 编辑状态
  const [editingPortfolio, setEditingPortfolio] = useState<Portfolio | null>(
    null,
  );
  const [editingHolding, setEditingHolding] =
    useState<InvestmentHolding | null>(null);
  const [isAddPortfolioOpen, setIsAddPortfolioOpen] = useState(false);
  const [isAddHoldingOpen, setIsAddHoldingOpen] = useState(false);

  // 获取当前组合
  const currentPortfolio = portfolios.find((p) => p.id === activePortfolio);

  // 添加投资组合
  const addPortfolio = (portfolioData: Partial<Portfolio>) => {
    if (portfolios.length >= FREE_TIER_LIMITS.maxPortfolios) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxPortfolios} portfolio. Upgrade for unlimited portfolios.`,
      );
      return;
    }

    const newPortfolio: Portfolio = {
      id: Date.now().toString(),
      name: portfolioData.name || "新投资组合",
      description: portfolioData.description || "",
      strategy: portfolioData.strategy || "",
      riskLevel: portfolioData.riskLevel || "moderate",
      currency: portfolioData.currency || "USD",
      holdings: [],
      totalValue: 0,
      totalInvested: 0,
      totalGain: 0,
      totalGainPercent: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
    };

    setPortfolios([...portfolios, newPortfolio]);
    setActivePortfolio(newPortfolio.id);
    setIsAddPortfolioOpen(false);
  };

  // 编辑投资组合
  const updatePortfolio = (
    portfolioId: string,
    updates: Partial<Portfolio>,
  ) => {
    setPortfolios(
      portfolios.map((p) =>
        p.id === portfolioId ? { ...p, ...updates, updatedAt: new Date() } : p,
      ),
    );
    setEditingPortfolio(null);
  };

  // 添加投资标的
  const addHolding = (holdingData: Partial<InvestmentHolding>) => {
    if (!currentPortfolio) return;

    if (
      currentPortfolio.holdings.length >=
      FREE_TIER_LIMITS.maxHoldingsPerPortfolio
    ) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxHoldingsPerPortfolio} holdings per portfolio. Upgrade for unlimited holdings.`,
      );
      return;
    }

    const newHolding: InvestmentHolding = {
      id: Date.now().toString(),
      name: holdingData.name || "新投资标的",
      symbol: holdingData.symbol,
      type: holdingData.type || "etf",
      investmentMethod: holdingData.investmentMethod || "dca",
      currency: holdingData.currency || currentPortfolio.currency,
      targetAllocation: holdingData.targetAllocation || 0,
      currentValue: 0,
      totalInvested: 0,
      totalShares: 0,
      averageCost: 0,
      isVisible: true,
      color:
        HOLDING_COLORS[
          currentPortfolio.holdings.length % HOLDING_COLORS.length
        ],
      unrealizedGain: 0,
      unrealizedGainPercent: 0,
      realizedGain: 0,
      records: [],
    };

    updatePortfolio(activePortfolio, {
      holdings: [...currentPortfolio.holdings, newHolding],
    });
    setIsAddHoldingOpen(false);
  };

  // 编辑投资标的
  const updateHolding = (
    holdingId: string,
    updates: Partial<InvestmentHolding>,
  ) => {
    if (!currentPortfolio) return;

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.map((h) =>
        h.id === holdingId ? { ...h, ...updates } : h,
      ),
    });
    setEditingHolding(null);
  };

  // 删除投资标的
  const deleteHolding = (holdingId: string) => {
    if (!currentPortfolio) return;

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.filter((h) => h.id !== holdingId),
    });
  };

  // 切换标的可见性
  const toggleHoldingVisibility = (holdingId: string) => {
    if (!currentPortfolio) return;

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.map((h) =>
        h.id === holdingId ? { ...h, isVisible: !h.isVisible } : h,
      ),
    });
  };

  // 添加投资记录
  const addRecord = (
    holdingId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    if (!currentPortfolio) return;

    const newRecord: InvestmentRecord = {
      id: Date.now().toString(),
      date: recordData.date || new Date(),
      amount: recordData.amount || 0,
      price: recordData.price,
      shares: recordData.shares,
      fees: recordData.fees,
      note: recordData.note,
      type: recordData.type || "buy",
    };

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.map((h) =>
        h.id === holdingId ? { ...h, records: [...h.records, newRecord] } : h,
      ),
    });
  };

  // 更新投资记录
  const updateRecord = (
    holdingId: string,
    recordId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    if (!currentPortfolio) return;

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.map((h) =>
        h.id === holdingId
          ? {
              ...h,
              records: h.records.map((r) =>
                r.id === recordId ? { ...r, ...recordData } : r,
              ),
            }
          : h,
      ),
    });
  };

  // 删除投资记录
  const deleteRecord = (holdingId: string, recordId: string) => {
    if (!currentPortfolio) return;

    updatePortfolio(activePortfolio, {
      holdings: currentPortfolio.holdings.map((h) =>
        h.id === holdingId
          ? {
              ...h,
              records: h.records.filter((r) => r.id !== recordId),
            }
          : h,
      ),
    });
  };

  return (
    <section id="portfolio-system" className="bg-background py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto mb-16 max-w-2xl text-center">
          <Badge variant="secondary" className="mb-4">
            <Briefcase className="mr-2 h-3 w-3" />
            Professional Portfolio Management
          </Badge>

          <h2 className="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
            Build Your Investment Portfolio
          </h2>

          <p className="text-muted-foreground mt-4 text-lg">
            Create diversified portfolios with multiple holdings, track real
            investments, and analyze your performance with professional tools.
          </p>
        </div>

        {/* Portfolio Selector & Controls */}
        <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-4">
            {/* Portfolio Selector */}
            <Select value={activePortfolio} onValueChange={setActivePortfolio}>
              <SelectTrigger className="w-64">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {portfolios.map((portfolio) => (
                  <SelectItem key={portfolio.id} value={portfolio.id}>
                    {portfolio.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* View Mode Tabs */}
            <div className="flex rounded-lg border">
              <Button
                variant={viewMode === "overview" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("overview")}
              >
                Overview
              </Button>
              <Button
                variant={viewMode === "holdings" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("holdings")}
              >
                Holdings & Records
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Dialog
              open={isAddPortfolioOpen}
              onOpenChange={setIsAddPortfolioOpen}
            >
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  New Portfolio
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Portfolio</DialogTitle>
                  <DialogDescription>
                    Create a new investment portfolio. You can add holdings
                    after creating the portfolio.
                  </DialogDescription>
                </DialogHeader>
                <PortfolioForm onSave={addPortfolio} />
              </DialogContent>
            </Dialog>

            <Dialog open={isAddHoldingOpen} onOpenChange={setIsAddHoldingOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Holding
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Investment Holding</DialogTitle>
                  <DialogDescription>
                    Add a new investment holding to your portfolio.
                  </DialogDescription>
                </DialogHeader>
                <HoldingForm
                  onSave={addHolding}
                  currency={currentPortfolio?.currency || "USD"}
                  isDisabled={
                    currentPortfolio
                      ? currentPortfolio.holdings.length >=
                        FREE_TIER_LIMITS.maxHoldingsPerPortfolio
                      : false
                  }
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Main Content */}
        {currentPortfolio && (
          <>
            {viewMode === "overview" && (
              <OverviewView
                portfolio={currentPortfolio}
                onEditPortfolio={setEditingPortfolio}
                onToggleHoldingVisibility={toggleHoldingVisibility}
              />
            )}

            {viewMode === "holdings" && (
              <HoldingsView
                portfolio={currentPortfolio}
                onEditHolding={setEditingHolding}
                onDeleteHolding={deleteHolding}
                onToggleVisibility={toggleHoldingVisibility}
                onAddRecord={addRecord}
                onUpdateRecord={updateRecord}
                onDeleteRecord={deleteRecord}
              />
            )}
          </>
        )}

        {/* Edit Dialogs */}
        {editingPortfolio && (
          <Dialog
            open={!!editingPortfolio}
            onOpenChange={() => setEditingPortfolio(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Portfolio</DialogTitle>
              </DialogHeader>
              <PortfolioForm
                initialData={editingPortfolio}
                onSave={(updates) =>
                  updatePortfolio(editingPortfolio.id, updates)
                }
              />
            </DialogContent>
          </Dialog>
        )}

        {editingHolding && (
          <Dialog
            open={!!editingHolding}
            onOpenChange={() => setEditingHolding(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Holding</DialogTitle>
              </DialogHeader>
              <HoldingForm
                initialData={editingHolding}
                onSave={(updates) => updateHolding(editingHolding.id, updates)}
                currency={currentPortfolio?.currency || "USD"}
              />
            </DialogContent>
          </Dialog>
        )}

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-primary/5 border-primary/20 mx-auto max-w-2xl rounded-2xl border p-8">
            <Sparkles className="text-primary mx-auto mb-4 h-12 w-12" />
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Unlock Professional Portfolio Features
            </h3>
            <p className="text-muted-foreground mb-6">
              Unlimited portfolios and holdings, advanced analytics, rebalancing
              tools, tax optimization, and institutional-grade reporting.
            </p>
            <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/signup">
                  Start Pro Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/dashboard">View Full Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
