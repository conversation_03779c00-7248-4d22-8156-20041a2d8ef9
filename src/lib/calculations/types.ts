// 投资计算器核心类型定义

export interface BaseCalculationParams {
  currency: string;
  name?: string;
}

export interface LumpSumParams extends BaseCalculationParams {
  principal: number;
  annualRate: number;
  years: number;
  compoundingFrequency?: number; // 默认12 (月复利)
}

export interface DCAParams extends BaseCalculationParams {
  initialInvestment?: number;
  monthlyContribution: number;
  annualRate: number;
  years: number;
  contributionIncrease?: number; // 年度贡献增长率
}

export interface RetirementParams extends BaseCalculationParams {
  currentAge: number;
  retirementAge: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturn: number;
  inflationRate?: number;
  retirementGoal: number;
}

export interface SavingsGoalParams extends BaseCalculationParams {
  targetAmount: number;
  timeframe: number; // 年数
  initialAmount?: number;
  expectedReturn: number;
  contributionFrequency: "monthly" | "quarterly" | "annually";
}

export type CalculationParams =
  | LumpSumParams
  | DCAParams
  | RetirementParams
  | SavingsGoalParams;

// 计算结果类型
export interface BaseCalculationResult {
  finalAmount: number;
  totalContributions: number;
  totalGains: number;
  effectiveRate: number;
  calculationType: string;
  currency: string;
}

export interface LumpSumResult extends BaseCalculationResult {
  principal: number;
  yearlyBreakdown: YearlyBreakdown[];
}

export interface DCAResult extends BaseCalculationResult {
  monthlyBreakdown: MonthlyBreakdown[];
  averageCostBasis: number;
}

export interface RetirementResult extends BaseCalculationResult {
  shortfall: number;
  recommendedMonthlyContribution: number;
  inflationAdjustedGoal: number;
}

export interface SavingsGoalResult extends BaseCalculationResult {
  requiredMonthlyContribution: number;
  timeToGoal: number;
  projectedShortfall?: number;
}

export interface YearlyBreakdown {
  year: number;
  startingBalance: number;
  contribution: number;
  interest: number;
  endingBalance: number;
}

export interface MonthlyBreakdown {
  month: number;
  year: number;
  contribution: number;
  balance: number;
  cumulativeContributions: number;
  cumulativeGains: number;
}

// 存储相关类型
export interface StoredCalculation {
  id: string;
  name: string;
  type: string;
  parameters: CalculationParams;
  results: BaseCalculationResult;
  currency: string;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

// 对比分析类型
export interface ComparisonData {
  id: string;
  name: string;
  calculations: StoredCalculation[];
  createdAt: string;
}

// 数据同步类型
export interface DataConflict {
  local: StoredCalculation;
  cloud: StoredCalculation;
}

export interface MergeResult {
  merged: StoredCalculation[];
  conflicts: DataConflict[];
}

// 货币类型
export type SupportedCurrency =
  | "USD"
  | "EUR"
  | "GBP"
  | "JPY"
  | "CNY"
  | "KRW"
  | "CAD"
  | "AUD";

// 投资记录接口（用户真实投资记录）
export interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  currency: SupportedCurrency;
  type: "initial" | "regular" | "manual" | "bonus";
  note?: string;
  exchangeRate?: number; // 相对于显示货币的汇率
  marketPrice?: number; // 投资时的市场价格（用于计算份额）
  shares?: number; // 购买的份额数量
  fees?: number; // 手续费
}

// 定投统计数据
export interface DCAStatistics {
  totalInvested: number;
  totalRecords: number;
  averageAmount: number;
  investmentFrequency: number; // 平均投资间隔（天）
  largestInvestment: number;
  smallestInvestment: number;
  monthlyTrend: {
    month: string;
    amount: number;
    count: number;
  }[];
  consistencyScore: number; // 投资一致性评分 0-100
}

// 资产配置接口
export interface AssetAllocation {
  id: string;
  name: string;
  percentage: number;
  color: string;
  investmentCurrency: SupportedCurrency;
  amount: number;
  calculationType: "lump_sum" | "dca" | "retirement" | "savings_goal";
  params: LumpSumParams | DCAParams | RetirementParams | SavingsGoalParams;
  result?: BaseCalculationResult;
  records?: InvestmentRecord[]; // 定投记录
}

// 投资组合接口
export interface InvestmentPortfolio {
  id: string;
  name: string;
  description?: string;
  displayCurrency: SupportedCurrency;
  assets: AssetAllocation[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  totalValue?: number;
  totalGains?: number;
  totalReturn?: number;
}

export interface CurrencyInfo {
  code: SupportedCurrency;
  symbol: string;
  name: string;
  locale: string;
}

// 图表数据类型
export interface ChartDataPoint {
  period: number;
  balance: number;
  contributions: number;
  gains: number;
  label?: string;
}

// 报告生成类型
export interface ReportOptions {
  includeCharts: boolean;
  includeBreakdown: boolean;
  template: "standard" | "detailed" | "summary";
}

// 用户偏好类型
export interface UserPreferences {
  defaultCurrency: SupportedCurrency;
  defaultCalculationType: string;
  chartPreferences: {
    showContributions: boolean;
    showGains: boolean;
    chartType: "line" | "area" | "bar";
  };
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 验证错误类型
export interface ValidationError {
  field: string;
  message: string;
}

export interface CalculationValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}
