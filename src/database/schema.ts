import {
  pgTable,
  text,
  integer,
  timestamp,
  boolean,
  uuid,
  index,
  pgEnum,
  jsonb,
  decimal,
} from "drizzle-orm/pg-core";

// 定义用户角色枚举
export const userRoleEnum = pgEnum("user_role", [
  "user",
  "admin",
  "super_admin",
]);

// 投资计算类型枚举
export const calculationTypeEnum = pgEnum("calculation_type", [
  "lump_sum",
  "dca",
  "retirement",
  "savings_goal",
]);

// 货币枚举
export const currencyEnum = pgEnum("currency", [
  "USD",
  "EUR",
  "GBP",
  "JPY",
  "CNY",
  "KRW",
  "CAD",
  "AUD",
]);

// 存储类型枚举
export const storageTypeEnum = pgEnum("storage_type", [
  "local",
  "cloud",
  "synced",
]);

// 订阅状态枚举
export const subscriptionStatusEnum = pgEnum("subscription_status", [
  "free",
  "premium",
  "expired",
]);

export const users = pgTable("users", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  role: userRoleEnum("role").notNull().default("user"),
  paymentProviderCustomerId: text("paymentProviderCustomerId").unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const sessions = pgTable("sessions", {
  id: text("id").primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  // Pre-parsed userAgent fields for performance optimization
  os: text("os"),
  browser: text("browser"),
  deviceType: text("deviceType"),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const accounts = pgTable(
  "accounts",
  {
    id: text("id").primaryKey(),
    accountId: text("accountId").notNull(),
    providerId: text("providerId").notNull(),
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    accessToken: text("accessToken"),
    refreshToken: text("refreshToken"),
    idToken: text("idToken"),
    accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
    refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
    scope: text("scope"),
    password: text("password"),
    createdAt: timestamp("createdAt").notNull(),
    updatedAt: timestamp("updatedAt").notNull(),
  },
  (table) => {
    return {
      userIdx: index("accounts_userId_idx").on(table.userId),
    };
  },
);

export const verifications = pgTable("verifications", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt"),
  updatedAt: timestamp("updatedAt"),
});

// Subscription table to store user subscription information
export const subscriptions = pgTable(
  "subscriptions",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    customerId: text("customerId").notNull(),
    subscriptionId: text("subscriptionId").notNull().unique(),
    productId: text("productId").notNull(),
    status: text("status").notNull(),
    currentPeriodStart: timestamp("currentPeriodStart"),
    currentPeriodEnd: timestamp("currentPeriodEnd"),
    canceledAt: timestamp("canceledAt"),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdx: index("subscriptions_userId_idx").on(table.userId),
      customerIdIdx: index("subscriptions_customerId_idx").on(table.customerId),
    };
  },
);

// Payment records table to store payment history
export const payments = pgTable(
  "payments",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    customerId: text("customerId").notNull(),
    subscriptionId: text("subscriptionId"),
    productId: text("productId").notNull(),
    paymentId: text("paymentId").notNull().unique(),
    amount: integer("amount").notNull(),
    currency: text("currency").notNull().default("usd"),
    status: text("status").notNull(),
    paymentType: text("paymentType").notNull(),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdx: index("payments_userId_idx").on(table.userId),
    };
  },
);

// Webhook events table to ensure idempotency
export const webhookEvents = pgTable(
  "webhook_events",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    eventId: text("eventId").notNull().unique(), // Unique identifier from webhook provider
    eventType: text("eventType").notNull(),
    provider: text("provider").notNull().default("creem"), // Support multiple providers
    processed: boolean("processed").notNull().default(true),
    processedAt: timestamp("processedAt").notNull().defaultNow(),
    payload: text("payload"), // Store original payload for debugging
    createdAt: timestamp("createdAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      eventIdIdx: index("webhook_events_eventId_idx").on(table.eventId),
      providerIdx: index("webhook_events_provider_idx").on(table.provider),
    };
  },
);

// 投资计算记录表
export const investmentCalculations = pgTable(
  "investment_calculations",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId").references(() => users.id, { onDelete: "cascade" }),
    name: text("name").notNull(),
    calculationType: calculationTypeEnum("calculation_type").notNull(),
    parameters: jsonb("parameters").notNull(), // 计算参数
    results: jsonb("results").notNull(), // 计算结果
    currency: currencyEnum("currency").notNull().default("USD"),
    isFavorite: boolean("is_favorite").notNull().default(false),
    storageType: storageTypeEnum("storage_type").notNull().default("local"),
    isArchived: boolean("is_archived").notNull().default(false),
    archivedAt: timestamp("archived_at"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdIdx: index("investment_calculations_userId_idx").on(table.userId),
      typeIdx: index("investment_calculations_type_idx").on(
        table.calculationType,
      ),
      createdAtIdx: index("investment_calculations_createdAt_idx").on(
        table.createdAt,
      ),
      favoriteIdx: index("investment_calculations_favorite_idx").on(
        table.isFavorite,
      ),
    };
  },
);

// 投资组合对比表
export const portfolioComparisons = pgTable(
  "portfolio_comparisons",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: text("name").notNull(),
    calculationIds: jsonb("calculation_ids").notNull(), // UUID数组
    comparisonSettings: jsonb("comparison_settings").default("{}"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdIdx: index("portfolio_comparisons_userId_idx").on(table.userId),
      createdAtIdx: index("portfolio_comparisons_createdAt_idx").on(
        table.createdAt,
      ),
    };
  },
);

// 用户偏好设置表
export const userPreferences = pgTable(
  "user_preferences",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId")
      .unique()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    defaultCurrency: currencyEnum("default_currency").notNull().default("USD"),
    defaultCalculationType: calculationTypeEnum("default_calculation_type")
      .notNull()
      .default("lump_sum"),
    chartPreferences: jsonb("chart_preferences").default("{}"),
    notificationSettings: jsonb("notification_settings").default("{}"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdIdx: index("user_preferences_userId_idx").on(table.userId),
    };
  },
);

// 数据保留策略表
export const dataRetentionPolicies = pgTable(
  "data_retention_policies",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("userId")
      .unique()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    subscriptionStatus: subscriptionStatusEnum("subscription_status")
      .notNull()
      .default("free"),
    subscriptionExpiredAt: timestamp("subscription_expired_at"),
    dataBackupGeneratedAt: timestamp("data_backup_generated_at"),
    gracePeriodEndsAt: timestamp("grace_period_ends_at"), // 90天宽限期结束时间
    reminderSentAt: timestamp("reminder_sent_at"), // 最后一次提醒邮件发送时间
    autoBackupEnabled: boolean("auto_backup_enabled").notNull().default(true),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdIdx: index("data_retention_policies_userId_idx").on(table.userId),
      subscriptionStatusIdx: index(
        "data_retention_policies_subscription_status_idx",
      ).on(table.subscriptionStatus),
      gracePeriodIdx: index("data_retention_policies_grace_period_idx").on(
        table.gracePeriodEndsAt,
      ),
    };
  },
);
