CREATE TYPE "public"."calculation_type" AS ENUM('lump_sum', 'dca', 'retirement', 'savings_goal');--> statement-breakpoint
CREATE TYPE "public"."currency" AS ENUM('USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'CAD', 'AUD');--> statement-breakpoint
CREATE TYPE "public"."storage_type" AS ENUM('local', 'cloud', 'synced');--> statement-breakpoint
CREATE TYPE "public"."subscription_status" AS ENUM('free', 'premium', 'expired');--> statement-breakpoint
CREATE TABLE "data_retention_policies" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" text NOT NULL,
	"subscription_status" "subscription_status" DEFAULT 'free' NOT NULL,
	"subscription_expired_at" timestamp,
	"data_backup_generated_at" timestamp,
	"grace_period_ends_at" timestamp,
	"reminder_sent_at" timestamp,
	"auto_backup_enabled" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "data_retention_policies_userId_unique" UNIQUE("userId")
);
--> statement-breakpoint
CREATE TABLE "investment_calculations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" text,
	"name" text NOT NULL,
	"calculation_type" "calculation_type" NOT NULL,
	"parameters" jsonb NOT NULL,
	"results" jsonb NOT NULL,
	"currency" "currency" DEFAULT 'USD' NOT NULL,
	"is_favorite" boolean DEFAULT false NOT NULL,
	"storage_type" "storage_type" DEFAULT 'local' NOT NULL,
	"is_archived" boolean DEFAULT false NOT NULL,
	"archived_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "portfolio_comparisons" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" text NOT NULL,
	"name" text NOT NULL,
	"calculation_ids" jsonb NOT NULL,
	"comparison_settings" jsonb DEFAULT '{}',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_preferences" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" text NOT NULL,
	"default_currency" "currency" DEFAULT 'USD' NOT NULL,
	"default_calculation_type" "calculation_type" DEFAULT 'lump_sum' NOT NULL,
	"chart_preferences" jsonb DEFAULT '{}',
	"notification_settings" jsonb DEFAULT '{}',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "user_preferences_userId_unique" UNIQUE("userId")
);
--> statement-breakpoint
ALTER TABLE "data_retention_policies" ADD CONSTRAINT "data_retention_policies_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "investment_calculations" ADD CONSTRAINT "investment_calculations_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "portfolio_comparisons" ADD CONSTRAINT "portfolio_comparisons_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_preferences" ADD CONSTRAINT "user_preferences_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "data_retention_policies_userId_idx" ON "data_retention_policies" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "data_retention_policies_subscription_status_idx" ON "data_retention_policies" USING btree ("subscription_status");--> statement-breakpoint
CREATE INDEX "data_retention_policies_grace_period_idx" ON "data_retention_policies" USING btree ("grace_period_ends_at");--> statement-breakpoint
CREATE INDEX "investment_calculations_userId_idx" ON "investment_calculations" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "investment_calculations_type_idx" ON "investment_calculations" USING btree ("calculation_type");--> statement-breakpoint
CREATE INDEX "investment_calculations_createdAt_idx" ON "investment_calculations" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "investment_calculations_favorite_idx" ON "investment_calculations" USING btree ("is_favorite");--> statement-breakpoint
CREATE INDEX "portfolio_comparisons_userId_idx" ON "portfolio_comparisons" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "portfolio_comparisons_createdAt_idx" ON "portfolio_comparisons" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "user_preferences_userId_idx" ON "user_preferences" USING btree ("userId");