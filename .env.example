# Database URL
DATABASE_URL="postgres://<user>:<password>@<url>:<port>/<db_name>"

# --- Database connection pool settings for traditional servers ---
# These values are suitable for development and will be overridden by the logic
# in `lib/database/connection.ts` in a serverless environment.
# DB_POOL_SIZE=20
# DB_IDLE_TIMEOUT=300
# DB_MAX_LIFETIME=14400
# DB_CONNECT_TIMEOUT=30

# Application settings
NEXT_PUBLIC_APP_URL="required"

# Authentication credentials
GOOGLE_CLIENT_ID="optional"
GOOGLE_CLIENT_SECRET="optional"

GITHUB_CLIENT_ID="optional"
GITHUB_CLIENT_SECRET="optional"

LINKEDIN_CLIENT_ID="optional"
LINKEDIN_CLIENT_SECRET="optional"

# Better Auth Secret
BETTER_AUTH_SECRET="required"

# API keys
RESEND_API_KEY="required"



# Payments
# test_mode / live_mode
CREEM_ENVIRONMENT=test_mode
CREEM_API_KEY=creem_test_api_key_here
CREEM_WEBHOOK_SECRET=whsec_test_secret_here