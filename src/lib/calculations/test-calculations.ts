// 测试投资计算引擎的功能

import { InvestmentCalculationEngine } from "./engines";
import { CalculationValidator } from "./validators";
import { ResultFormatter } from "./formatters";
import type {
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
} from "./types";

// 测试数据
const testLumpSum: LumpSumParams = {
  principal: 10000,
  annualRate: 7,
  years: 10,
  currency: "USD",
  name: "Test Lump Sum",
};

const testDCA: DCAParams = {
  initialInvestment: 5000,
  monthlyContribution: 500,
  annualRate: 7,
  years: 10,
  currency: "USD",
  name: "Test DCA",
};

const testRetirement: RetirementParams = {
  currentAge: 30,
  retirementAge: 65,
  currentSavings: 50000,
  monthlyContribution: 1000,
  expectedReturn: 7,
  inflationRate: 2.5,
  retirementGoal: 1000000,
  currency: "USD",
  name: "Test Retirement",
};

const testSavingsGoal: SavingsGoalParams = {
  targetAmount: 100000,
  timeframe: 10,
  initialAmount: 10000,
  expectedReturn: 7,
  contributionFrequency: "monthly",
  currency: "USD",
  name: "Test Savings Goal",
};

export function runCalculationTests() {
  console.log("🧮 Testing Investment Calculation Engine...\n");

  // 测试一次性投资
  console.log("1. Testing Lump Sum Calculation:");
  const lumpSumValidation = CalculationValidator.validateLumpSum(testLumpSum);
  console.log(
    "   Validation:",
    lumpSumValidation.isValid ? "✅ Valid" : "❌ Invalid",
  );

  if (lumpSumValidation.isValid) {
    const lumpSumResult =
      InvestmentCalculationEngine.calculateLumpSum(testLumpSum);
    const formattedLumpSum = ResultFormatter.formatLumpSumResult(lumpSumResult);
    console.log("   Result:", {
      finalAmount: lumpSumResult.finalAmount.toFixed(2),
      totalGains: lumpSumResult.totalGains.toFixed(2),
      effectiveRate: (lumpSumResult.effectiveRate * 100).toFixed(2) + "%",
    });
  }

  // 测试定投
  console.log("\n2. Testing DCA Calculation:");
  const dcaValidation = CalculationValidator.validateDCA(testDCA);
  console.log(
    "   Validation:",
    dcaValidation.isValid ? "✅ Valid" : "❌ Invalid",
  );

  if (dcaValidation.isValid) {
    const dcaResult = InvestmentCalculationEngine.calculateDCA(testDCA);
    const formattedDCA = ResultFormatter.formatDCAResult(dcaResult);
    console.log("   Result:", {
      finalAmount: dcaResult.finalAmount.toFixed(2),
      totalContributions: dcaResult.totalContributions.toFixed(2),
      totalGains: dcaResult.totalGains.toFixed(2),
      averageCostBasis: dcaResult.averageCostBasis.toFixed(2),
    });
  }

  // 测试退休规划
  console.log("\n3. Testing Retirement Planning:");
  const retirementValidation =
    CalculationValidator.validateRetirement(testRetirement);
  console.log(
    "   Validation:",
    retirementValidation.isValid ? "✅ Valid" : "❌ Invalid",
  );

  if (retirementValidation.isValid) {
    const retirementResult =
      InvestmentCalculationEngine.calculateRetirement(testRetirement);
    const formattedRetirement =
      ResultFormatter.formatRetirementResult(retirementResult);
    console.log("   Result:", {
      finalAmount: retirementResult.finalAmount.toFixed(2),
      shortfall: retirementResult.shortfall.toFixed(2),
      recommendedMonthly:
        retirementResult.recommendedMonthlyContribution.toFixed(2),
      inflationAdjustedGoal: retirementResult.inflationAdjustedGoal.toFixed(2),
    });
  }

  // 测试储蓄目标
  console.log("\n4. Testing Savings Goal:");
  const savingsValidation =
    CalculationValidator.validateSavingsGoal(testSavingsGoal);
  console.log(
    "   Validation:",
    savingsValidation.isValid ? "✅ Valid" : "❌ Invalid",
  );

  if (savingsValidation.isValid) {
    const savingsResult =
      InvestmentCalculationEngine.calculateSavingsGoal(testSavingsGoal);
    const formattedSavings =
      ResultFormatter.formatSavingsGoalResult(savingsResult);
    console.log("   Result:", {
      finalAmount: savingsResult.finalAmount.toFixed(2),
      requiredMonthly: savingsResult.requiredMonthlyContribution.toFixed(2),
      timeToGoal: savingsResult.timeToGoal.toFixed(1) + " years",
      totalContributions: savingsResult.totalContributions.toFixed(2),
    });
  }

  console.log("\n✅ All calculation tests completed!");
}

// 测试错误处理
export function runValidationTests() {
  console.log("\n🔍 Testing Validation...\n");

  // 测试无效参数
  const invalidLumpSum: LumpSumParams = {
    principal: -1000, // 负数应该无效
    annualRate: 150, // 超过100%应该无效
    years: -5, // 负年数应该无效
    currency: "USD",
    name: "Invalid Test",
  };

  const validation = CalculationValidator.validateLumpSum(invalidLumpSum);
  console.log(
    "Invalid parameters validation:",
    validation.isValid ? "❌ Should be invalid" : "✅ Correctly invalid",
  );

  if (!validation.isValid) {
    console.log("Validation errors:");
    validation.errors.forEach((error) => {
      console.log(`   - ${error.field}: ${error.message}`);
    });
  }

  console.log("\n✅ Validation tests completed!");
}

// 测试货币格式化
export function runFormattingTests() {
  console.log("\n💰 Testing Currency Formatting...\n");

  const testAmount = 1234567.89;
  const currencies = ["USD", "EUR", "GBP", "JPY", "CNY"] as const;

  currencies.forEach((currency) => {
    const formatted = ResultFormatter.formatCurrency(testAmount, currency);
    const compact = ResultFormatter.formatCurrency(testAmount, currency, {
      compact: true,
    });
    console.log(`${currency}: ${formatted} (compact: ${compact})`);
  });

  console.log("\n✅ Formatting tests completed!");
}

// 运行所有测试
export function runAllTests() {
  runCalculationTests();
  runValidationTests();
  runFormattingTests();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests();
}
