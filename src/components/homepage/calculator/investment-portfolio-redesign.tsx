"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calculator,
  TrendingUp,
  DollarSign,
  PieChart,
  LineChart,
  Plus,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  Sparkles,
  ArrowRight,
  Settings,
  Target,
  Calendar,
  BarChart3,
} from "lucide-react";
import Link from "next/link";
import { OverviewView, StrategiesView, GoalsView } from "./portfolio-views";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 重新定义的数据结构
interface InvestmentStrategy {
  id: string;
  name: string;
  type: "lump_sum" | "dca";
  currency: SupportedCurrency;
  isVisible: boolean;
  color: string;
  // 策略参数
  params: {
    initialAmount?: number;
    monthlyAmount?: number;
    expectedReturn: number;
    timeHorizon: number;
  };
  // 真实记录（仅DCA）
  records?: InvestmentRecord[];
  // 计算结果
  result?: {
    currentValue: number;
    totalInvested: number;
    totalGains: number;
    annualizedReturn: number;
  };
}

interface FinancialGoal {
  id: string;
  name: string;
  type: "retirement" | "savings_goal";
  targetAmount: number;
  targetDate: Date;
  currentProgress: number;
  currency: SupportedCurrency;
  isActive: boolean;
}

interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  note?: string;
  marketPrice?: number;
  shares?: number;
}

interface Portfolio {
  id: string;
  name: string;
  displayCurrency: SupportedCurrency;
  strategies: InvestmentStrategy[];
  goals: FinancialGoal[];
  createdAt: Date;
  updatedAt: Date;
}

// 预定义颜色
const STRATEGY_COLORS = [
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#ec4899",
  "#6366f1",
];

// 免费版限制
const FREE_TIER_LIMITS = {
  maxStrategies: 3,
  maxGoals: 2,
};

export function InvestmentPortfolioRedesign() {
  // 全局设置
  const [displayCurrency, setDisplayCurrency] =
    useState<SupportedCurrency>("USD");
  const [viewMode, setViewMode] = useState<"overview" | "strategies" | "goals">(
    "overview",
  );

  // 投资组合数据
  const [portfolio, setPortfolio] = useState<Portfolio>({
    id: "1",
    name: "My Investment Portfolio",
    displayCurrency: "USD",
    strategies: [
      {
        id: "1",
        name: "US Stock Index",
        type: "dca",
        currency: "USD",
        isVisible: true,
        color: STRATEGY_COLORS[0],
        params: {
          initialAmount: 10000,
          monthlyAmount: 1000,
          expectedReturn: 8,
          timeHorizon: 10,
        },
        records: [
          {
            id: "r1",
            date: new Date("2024-01-15"),
            amount: 10000,
            note: "Initial investment",
          },
          {
            id: "r2",
            date: new Date("2024-02-15"),
            amount: 1000,
            note: "Monthly DCA",
          },
          {
            id: "r3",
            date: new Date("2024-03-15"),
            amount: 800,
            note: "Reduced amount this month",
          },
        ],
      },
      {
        id: "2",
        name: "Bond Portfolio",
        type: "lump_sum",
        currency: "USD",
        isVisible: true,
        color: STRATEGY_COLORS[1],
        params: {
          initialAmount: 25000,
          expectedReturn: 4,
          timeHorizon: 5,
        },
      },
    ],
    goals: [
      {
        id: "g1",
        name: "Retirement Fund",
        type: "retirement",
        targetAmount: 1000000,
        targetDate: new Date("2054-12-31"),
        currentProgress: 45000,
        currency: "USD",
        isActive: true,
      },
      {
        id: "g2",
        name: "House Down Payment",
        type: "savings_goal",
        targetAmount: 100000,
        targetDate: new Date("2027-06-30"),
        currentProgress: 25000,
        currency: "USD",
        isActive: true,
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  // 图表显示控制
  const [chartSettings, setChartSettings] = useState({
    showStrategies: true,
    showGoals: false,
    timeRange: "5y" as "1y" | "3y" | "5y" | "10y",
    chartType: "area" as "area" | "line" | "bar",
  });

  // 编辑状态
  const [editingStrategy, setEditingStrategy] =
    useState<InvestmentStrategy | null>(null);
  const [editingGoal, setEditingGoal] = useState<FinancialGoal | null>(null);

  // 切换策略可见性
  const toggleStrategyVisibility = (strategyId: string) => {
    setPortfolio((prev) => ({
      ...prev,
      strategies: prev.strategies.map((s) =>
        s.id === strategyId ? { ...s, isVisible: !s.isVisible } : s,
      ),
    }));
  };

  // 删除策略
  const deleteStrategy = (strategyId: string) => {
    setPortfolio((prev) => ({
      ...prev,
      strategies: prev.strategies.filter((s) => s.id !== strategyId),
    }));
  };

  // 添加策略
  const addStrategy = () => {
    if (portfolio.strategies.length >= FREE_TIER_LIMITS.maxStrategies) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxStrategies} strategies. Upgrade for unlimited strategies.`,
      );
      return;
    }

    const newStrategy: InvestmentStrategy = {
      id: Date.now().toString(),
      name: `Strategy ${portfolio.strategies.length + 1}`,
      type: "lump_sum",
      currency: displayCurrency,
      isVisible: true,
      color:
        STRATEGY_COLORS[portfolio.strategies.length % STRATEGY_COLORS.length],
      params: {
        initialAmount: 10000,
        expectedReturn: 7,
        timeHorizon: 10,
      },
    };

    setPortfolio((prev) => ({
      ...prev,
      strategies: [...prev.strategies, newStrategy],
    }));
  };

  // 准备图表数据
  const chartData = prepareChartData(portfolio, chartSettings);

  return (
    <section id="investment-portfolio" className="bg-background py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto mb-16 max-w-2xl text-center">
          <Badge variant="secondary" className="mb-4">
            <Calculator className="mr-2 h-3 w-3" />
            Professional Investment Portfolio
          </Badge>

          <h2 className="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
            Build Your Investment Strategy
          </h2>

          <p className="text-muted-foreground mt-4 text-lg">
            Create investment strategies with real tracking, set financial
            goals, and visualize your progress with professional charts.
          </p>
        </div>

        {/* Main Interface */}
        <div className="mx-auto max-w-7xl">
          {/* Controls */}
          <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-4">
              <Select
                value={displayCurrency}
                onValueChange={(value: SupportedCurrency) =>
                  setDisplayCurrency(value)
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="JPY">JPY (¥)</SelectItem>
                  <SelectItem value="CNY">CNY (¥)</SelectItem>
                  <SelectItem value="CAD">CAD (C$)</SelectItem>
                  <SelectItem value="AUD">AUD (A$)</SelectItem>
                </SelectContent>
              </Select>

              <Tabs
                value={viewMode}
                onValueChange={(value: "overview" | "strategies" | "goals") =>
                  setViewMode(value)
                }
              >
                <TabsList>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="strategies">Strategies</TabsTrigger>
                  <TabsTrigger value="goals">Goals</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={addStrategy}>
                <Plus className="mr-2 h-4 w-4" />
                Add Strategy
              </Button>
              <Button variant="outline" size="sm">
                <Target className="mr-2 h-4 w-4" />
                Add Goal
              </Button>
            </div>
          </div>

          {/* Content based on view mode */}
          {viewMode === "overview" && (
            <OverviewView
              portfolio={portfolio}
              chartData={chartData}
              chartSettings={chartSettings}
              onChartSettingsChange={setChartSettings}
              onToggleStrategyVisibility={toggleStrategyVisibility}
              displayCurrency={displayCurrency}
            />
          )}

          {viewMode === "strategies" && (
            <StrategiesView
              strategies={portfolio.strategies}
              displayCurrency={displayCurrency}
              onEdit={setEditingStrategy}
              onDelete={deleteStrategy}
              onToggleVisibility={toggleStrategyVisibility}
            />
          )}

          {viewMode === "goals" && (
            <GoalsView
              goals={portfolio.goals}
              displayCurrency={displayCurrency}
              onEdit={setEditingGoal}
            />
          )}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-primary/5 border-primary/20 mx-auto max-w-2xl rounded-2xl border p-8">
            <Sparkles className="text-primary mx-auto mb-4 h-12 w-12" />
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Unlock Advanced Portfolio Features
            </h3>
            <p className="text-muted-foreground mb-6">
              Unlimited strategies and goals, advanced analytics, portfolio
              rebalancing, tax optimization, and professional reporting.
            </p>
            <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/signup">
                  Start Pro Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/dashboard">View Full Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 准备图表数据的辅助函数
function prepareChartData(portfolio: Portfolio, settings: any): any[] {
  // 这里会根据真实的投资记录和策略参数生成图表数据
  // 暂时返回模拟数据
  return [];
}
