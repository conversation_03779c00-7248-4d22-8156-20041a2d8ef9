"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DollarSign,
  TrendingUp,
  Target,
  Calendar,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  PieChart,
  Plus,
  Briefcase,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import {
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Area,
} from "recharts";
import { format<PERSON>urrency, CurrencyUtils } from "@/lib/utils/currency";
import { RecordForm } from "./portfolio-forms-v2";

// 类型定义
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

interface InvestmentHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  investmentMethod: "lump_sum" | "dca" | "mixed";
  currency: string;
  targetAllocation: number;
  currentValue: number;
  totalInvested: number;
  totalShares: number;
  averageCost: number;
  isVisible: boolean;
  color: string;
  records: InvestmentRecord[];
  unrealizedGain: number;
  unrealizedGainPercent: number;
  realizedGain: number;
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: string;
  holdings: InvestmentHolding[];
  totalValue: number;
  totalInvested: number;
  totalGain: number;
  totalGainPercent: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Overview View
export function OverviewView({
  portfolio,
  onEditPortfolio,
  onToggleHoldingVisibility,
}: {
  portfolio: Portfolio;
  onEditPortfolio: (portfolio: Portfolio) => void;
  onToggleHoldingVisibility: (holdingId: string) => void;
}) {
  // 准备饼图数据
  const pieData = portfolio.holdings.map((holding) => ({
    name: holding.symbol || holding.name,
    value: holding.currentValue,
    color: holding.color,
    allocation: holding.targetAllocation,
    actualAllocation:
      portfolio.totalValue > 0
        ? (holding.currentValue / portfolio.totalValue) * 100
        : 0,
  }));

  // 准备时间线数据（模拟）
  const timelineData = generateTimelineData(portfolio.holdings);

  return (
    <div className="space-y-8">
      {/* Portfolio Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                {portfolio.name}
              </CardTitle>
              <p className="text-muted-foreground mt-1">
                {portfolio.description}
              </p>
              <div className="mt-2 flex items-center gap-4">
                <Badge
                  variant={
                    portfolio.riskLevel === "conservative"
                      ? "secondary"
                      : portfolio.riskLevel === "moderate"
                        ? "default"
                        : "destructive"
                  }
                >
                  {portfolio.riskLevel}
                </Badge>
                <span className="text-muted-foreground text-sm">
                  {portfolio.strategy}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditPortfolio(portfolio)}
            >
              <Edit2 className="mr-2 h-4 w-4" />
              Edit Portfolio
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-4">
            <div className="text-center">
              <DollarSign className="mx-auto mb-2 h-8 w-8 text-blue-500" />
              <p className="text-muted-foreground text-sm">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(portfolio.totalValue, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <TrendingUp className="mx-auto mb-2 h-8 w-8 text-green-500" />
              <p className="text-muted-foreground text-sm">Total Gain</p>
              <p
                className={`text-2xl font-bold ${portfolio.totalGain >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {formatCurrency(portfolio.totalGain, portfolio.currency)}
              </p>
            </div>
            <div className="text-center">
              <BarChart3 className="mx-auto mb-2 h-8 w-8 text-purple-500" />
              <p className="text-muted-foreground text-sm">Return %</p>
              <p
                className={`text-2xl font-bold ${portfolio.totalGainPercent >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {CurrencyUtils.formatPercentage(portfolio.totalGainPercent)}
              </p>
            </div>
            <div className="text-center">
              <Target className="mx-auto mb-2 h-8 w-8 text-orange-500" />
              <p className="text-muted-foreground text-sm">Holdings</p>
              <p className="text-2xl font-bold">{portfolio.holdings.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Allocation Pie Chart */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Asset Allocation
              </CardTitle>
              <div className="flex gap-2">
                {portfolio.holdings.map((holding) => (
                  <Button
                    key={holding.id}
                    variant={holding.isVisible ? "default" : "outline"}
                    size="sm"
                    onClick={() => onToggleHoldingVisibility(holding.id)}
                    className="flex items-center gap-1"
                  >
                    <div
                      className="h-2 w-2 rounded-full"
                      style={{ backgroundColor: holding.color }}
                    />
                    {holding.isVisible ? (
                      <Eye className="h-3 w-3" />
                    ) : (
                      <EyeOff className="h-3 w-3" />
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={pieData.filter(
                    (_, index) => portfolio.holdings[index].isVisible,
                  )}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ actualAllocation }) =>
                    `${actualAllocation.toFixed(1)}%`
                  }
                >
                  {pieData
                    .filter((_, index) => portfolio.holdings[index].isVisible)
                    .map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [
                    formatCurrency(value, portfolio.currency),
                    "Value",
                  ]}
                />
                <Legend />
              </RechartsPieChart>
            </ResponsiveContainer>

            {/* Allocation vs Target */}
            <div className="mt-4 space-y-2">
              {portfolio.holdings
                .filter((h) => h.isVisible)
                .map((holding) => {
                  const actualAllocation =
                    portfolio.totalValue > 0
                      ? (holding.currentValue / portfolio.totalValue) * 100
                      : 0;
                  const difference =
                    actualAllocation - holding.targetAllocation;

                  return (
                    <div
                      key={holding.id}
                      className="flex items-center justify-between text-sm"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: holding.color }}
                        />
                        <span>{holding.symbol || holding.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span>{actualAllocation.toFixed(1)}%</span>
                        <span className="text-muted-foreground">vs</span>
                        <span>{holding.targetAllocation}%</span>
                        <span
                          className={`font-medium ${
                            Math.abs(difference) < 1
                              ? "text-green-600"
                              : Math.abs(difference) < 5
                                ? "text-yellow-600"
                                : "text-red-600"
                          }`}
                        >
                          ({difference > 0 ? "+" : ""}
                          {difference.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Portfolio Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={timelineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [
                    formatCurrency(value, portfolio.currency),
                    "Value",
                  ]}
                />
                <Legend />
                {portfolio.holdings
                  .filter((h) => h.isVisible)
                  .map((holding) => (
                    <Area
                      key={holding.id}
                      type="monotone"
                      dataKey={holding.symbol || holding.name}
                      stackId="1"
                      stroke={holding.color}
                      fill={holding.color}
                      fillOpacity={0.6}
                    />
                  ))}
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// 生成模拟时间线数据
function generateTimelineData(holdings: InvestmentHolding[]): any[] {
  const months = 12;
  const data = [];

  for (let i = 0; i < months; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - (months - 1 - i));

    const dataPoint: any = {
      date: date.toLocaleDateString("en-US", { month: "short" }),
    };

    holdings.forEach((holding) => {
      // 基于投资记录模拟增长
      const growth = 1 + (Math.random() * 0.2 - 0.1); // -10% to +10% monthly variation
      const baseValue = holding.totalInvested * (1 + i * 0.02); // 2% monthly growth trend
      dataPoint[holding.symbol || holding.name] = baseValue * growth;
    });

    data.push(dataPoint);
  }

  return data;
}

// Holdings View - 核心管理界面，可以直接管理投资记录
export function HoldingsView({
  portfolio,
  onEditHolding,
  onDeleteHolding,
  onToggleVisibility,
  onAddRecord,
  onUpdateRecord,
  onDeleteRecord,
}: {
  portfolio: Portfolio;
  onEditHolding: (holding: InvestmentHolding) => void;
  onDeleteHolding: (holdingId: string) => void;
  onToggleVisibility: (holdingId: string) => void;
  onAddRecord: (holdingId: string, record: Partial<InvestmentRecord>) => void;
  onUpdateRecord: (
    holdingId: string,
    recordId: string,
    record: Partial<InvestmentRecord>,
  ) => void;
  onDeleteRecord: (holdingId: string, recordId: string) => void;
}) {
  const [expandedHoldings, setExpandedHoldings] = useState<Set<string>>(
    new Set(),
  );
  const [editingRecord, setEditingRecord] = useState<{
    holdingId: string;
    record: InvestmentRecord;
  } | null>(null);
  const [addingRecordToHolding, setAddingRecordToHolding] = useState<
    string | null
  >(null);

  const toggleHoldingExpansion = (holdingId: string) => {
    const newExpanded = new Set(expandedHoldings);
    if (newExpanded.has(holdingId)) {
      newExpanded.delete(holdingId);
    } else {
      newExpanded.add(holdingId);
    }
    setExpandedHoldings(newExpanded);
  };

  const handleAddRecord = (
    holdingId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    onAddRecord(holdingId, recordData);
    setAddingRecordToHolding(null);
  };

  const handleUpdateRecord = (recordData: Partial<InvestmentRecord>) => {
    if (editingRecord) {
      onUpdateRecord(
        editingRecord.holdingId,
        editingRecord.record.id,
        recordData,
      );
      setEditingRecord(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Holdings Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Investment Holdings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Holdings</p>
              <p className="text-2xl font-bold">{portfolio.holdings.length}</p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Records</p>
              <p className="text-2xl font-bold">
                {portfolio.holdings.reduce(
                  (sum, h) => sum + h.records.length,
                  0,
                )}
              </p>
            </div>
            <div className="text-center">
              <p className="text-muted-foreground text-sm">Total Invested</p>
              <p className="text-2xl font-bold">
                {formatCurrency(portfolio.totalInvested, portfolio.currency)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Holdings List with Expandable Records */}
      <div className="space-y-4">
        {portfolio.holdings.map((holding) => {
          const isExpanded = expandedHoldings.has(holding.id);

          return (
            <Card key={holding.id} className="overflow-hidden">
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <CardHeader
                    className="hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => toggleHoldingExpansion(holding.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <div
                            className="h-4 w-4 rounded-full"
                            style={{ backgroundColor: holding.color }}
                          />
                        </div>

                        <div>
                          <CardTitle className="text-lg">
                            {holding.name}
                          </CardTitle>
                          <div className="mt-1 flex items-center gap-4">
                            {holding.symbol && (
                              <Badge variant="outline">{holding.symbol}</Badge>
                            )}
                            <Badge
                              variant={
                                holding.investmentMethod === "dca"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {holding.investmentMethod === "dca"
                                ? "DCA"
                                : holding.investmentMethod === "lump_sum"
                                  ? "Lump Sum"
                                  : "Mixed"}
                            </Badge>
                            <span className="text-muted-foreground text-sm">
                              {holding.records.length} records
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        {/* Key Metrics */}
                        <div className="text-right">
                          <p className="font-semibold">
                            {formatCurrency(
                              holding.currentValue,
                              holding.currency,
                            )}
                          </p>
                          <p
                            className={`text-sm ${
                              holding.unrealizedGain >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              holding.unrealizedGain,
                              holding.currency,
                            )}
                            (
                            {CurrencyUtils.formatPercentage(
                              holding.unrealizedGainPercent,
                            )}
                            )
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div
                          className="flex items-center gap-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setAddingRecordToHolding(holding.id)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onToggleVisibility(holding.id)}
                          >
                            {holding.isVisible ? (
                              <Eye className="h-4 w-4" />
                            ) : (
                              <EyeOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditHolding(holding)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteHolding(holding.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    {/* Holding Details */}
                    <div className="bg-muted/30 mb-6 grid grid-cols-2 gap-4 rounded-lg p-4 md:grid-cols-4">
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Invested
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.totalInvested,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Total Shares
                        </p>
                        <p className="font-semibold">
                          {holding.totalShares.toFixed(3)}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Average Cost
                        </p>
                        <p className="font-semibold">
                          {formatCurrency(
                            holding.averageCost,
                            holding.currency,
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Target Allocation
                        </p>
                        <p className="font-semibold">
                          {holding.targetAllocation}%
                        </p>
                      </div>
                    </div>

                    {/* Investment Records */}
                    <div>
                      <div className="mb-4 flex items-center justify-between">
                        <h4 className="font-medium">Investment Records</h4>
                        <Button
                          size="sm"
                          onClick={() => setAddingRecordToHolding(holding.id)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Record
                        </Button>
                      </div>

                      {holding.records.length === 0 ? (
                        <div className="bg-muted/20 rounded-lg py-8 text-center">
                          <Calendar className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                          <p className="text-muted-foreground">
                            No investment records yet
                          </p>
                          <p className="text-muted-foreground text-sm">
                            Add your first investment record
                          </p>
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Price</TableHead>
                              <TableHead>Shares</TableHead>
                              <TableHead>Fees</TableHead>
                              <TableHead>Note</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {holding.records.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell>
                                  {new Date(record.date).toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={
                                      record.type === "buy"
                                        ? "default"
                                        : record.type === "sell"
                                          ? "destructive"
                                          : "secondary"
                                    }
                                  >
                                    {record.type.toUpperCase()}
                                  </Badge>
                                </TableCell>
                                <TableCell className="font-medium">
                                  {formatCurrency(
                                    record.amount,
                                    holding.currency,
                                  )}
                                </TableCell>
                                <TableCell>
                                  {record.price
                                    ? formatCurrency(
                                        record.price,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.shares
                                    ? record.shares.toFixed(4)
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {record.fees
                                    ? formatCurrency(
                                        record.fees,
                                        holding.currency,
                                      )
                                    : "-"}
                                </TableCell>
                                <TableCell className="max-w-32 truncate">
                                  {record.note || "-"}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        setEditingRecord({
                                          holdingId: holding.id,
                                          record,
                                        })
                                      }
                                    >
                                      <Edit2 className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        onDeleteRecord(holding.id, record.id)
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      {/* Add Record Dialog */}
      {addingRecordToHolding && (
        <Dialog
          open={!!addingRecordToHolding}
          onOpenChange={() => setAddingRecordToHolding(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Investment Record</DialogTitle>
              <DialogDescription>
                Add a new investment record to{" "}
                {
                  portfolio.holdings.find((h) => h.id === addingRecordToHolding)
                    ?.name
                }
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              onSave={handleAddRecord.bind(null, addingRecordToHolding)}
              currency={
                portfolio.holdings.find((h) => h.id === addingRecordToHolding)
                  ?.currency || portfolio.currency
              }
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Record Dialog */}
      {editingRecord && (
        <Dialog
          open={!!editingRecord}
          onOpenChange={() => setEditingRecord(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Investment Record</DialogTitle>
              <DialogDescription>
                Edit investment record for{" "}
                {
                  portfolio.holdings.find(
                    (h) => h.id === editingRecord.holdingId,
                  )?.name
                }
              </DialogDescription>
            </DialogHeader>
            <RecordForm
              initialData={editingRecord.record}
              onSave={handleUpdateRecord}
              currency={
                portfolio.holdings.find((h) => h.id === editingRecord.holdingId)
                  ?.currency || portfolio.currency
              }
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
