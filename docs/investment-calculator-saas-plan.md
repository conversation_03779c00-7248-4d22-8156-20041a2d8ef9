# 投资收益计算器 SaaS 应用修改方案

## 📋 项目概述

基于现有的 UllrAI SaaS Starter Kit，将其改造为一个功能完善的投资收益计算器应用。该应用将保持现有的技术架构优势，同时添加投资计算相关的核心功能。

**核心设计理念**：

- **主页集成** - 在主页Hero区域添加简化版计算器，让用户无需登录即可体验核心功能
- **Dashboard增强** - 保留现有"Welcome Back"布局，在下方添加完整的投资计算器功能
- **渐进式体验** - 从免费的基础功能自然引导到付费的高级功能

## 🎯 核心功能特性

### 💰 多种计算模式

- **一次性投资计算器** - 计算单笔投资的复利增长
- **定期投资计算器 (DCA)** - 定投策略收益分析
- **退休规划计算器** - 退休储蓄目标规划
- **储蓄目标计算器** - 达成特定金额所需的投资计划

### 📊 数据可视化

- **交互式图表** - 使用现有 Recharts 展示投资增长曲线
- **多策略对比** - 同时显示多个投资策略的对比图表
- **年度/月度分解** - 详细的时间维度数据展示
- **收益构成分析** - 本金、收益、总额的可视化分解

### 💾 数据管理

- **本地存储优先** - 无需登录即可使用，数据存储在浏览器本地
- **云端同步** - 付费用户可享受跨设备数据同步
- **数据导入导出** - 支持 JSON 格式的完整数据备份
- **计算历史管理** - 保存、编辑、删除历史计算记录

#### 🔄 数据迁移和保留策略

**免费用户升级到付费用户**：

- ✅ **完整数据保留** - 所有本地存储的计算记录会自动同步到云端
- ✅ **无缝迁移** - 升级过程中不会丢失任何历史数据
- ✅ **数据合并** - 如果用户在多个设备上使用，会智能合并所有数据

**付费用户订阅到期**：

- ✅ **数据永久保留** - 云端数据不会被删除，只是暂停同步功能
- ✅ **只读访问** - 用户可以查看和导出所有历史数据
- ✅ **本地备份** - 系统会自动将云端数据备份到本地存储
- ⚠️ **功能限制** - 新的计算保存会受到免费版限制（最多3个）
- 🔄 **重新激活** - 重新订阅后立即恢复所有功能和数据同步

**数据安全保障**：

- 🛡️ **90天宽限期** - 订阅到期后90天内数据完全保留
- 📧 **到期提醒** - 订阅到期前7天、1天发送邮件提醒
- 💾 **自动导出** - 到期前自动生成数据备份文件供用户下载

### 🌍 多币种支持

- **主流货币** - 支持 USD, EUR, GBP, JPY, CNY, KRW, CAD, AUD
- **本地化格式** - 根据币种自动格式化数字显示
- **汇率无关** - 每个计算独立使用指定币种

### 📄 报告生成

- **PDF 导出** - 生成专业的投资分析报告
- **图表包含** - 报告中包含可视化图表
- **自定义模板** - 支持不同风格的报告模板

### 🔐 订阅模式

- **免费版功能（主页简化版）**
  - 完整的四种计算模式（一次性投资、定投、退休规划、储蓄目标）
  - 实时计算结果和基础图表展示
  - 投资策略对比功能（最多3个策略同时对比）
  - 本地数据存储（浏览器本地，不跨设备）
  - 基础数据导出（JSON格式）
  - 多币种支持
  - 响应式移动端体验

- **高级版功能（Dashboard完整版）**
  - 无限制保存和管理计算历史
  - 高级图表和深度分析（年度/月度分解、收益构成分析）
  - 云端数据同步（跨设备访问）
  - 专业PDF报告导出（包含图表和详细分析）
  - 投资组合管理和高级对比功能
  - 数据导入导出（多种格式）
  - 优先客户支持
  - 高级计算参数（通胀调整、税收考虑等）

## 🛠️ 技术栈

### 保持现有优势

- **前端框架**: Next.js 15 + React 19 + TypeScript
- **UI 组件**: Tailwind CSS v4 + shadcn/ui + Lucide Icons
- **数据库**: PostgreSQL + Drizzle ORM
- **认证系统**: Better-Auth (魔术链接 + OAuth)
- **支付集成**: Creem 支付服务
- **图表库**: Recharts (已集成)
- **表单处理**: React Hook Form + Zod

### 新增依赖

```json
{
  "dependencies": {
    "jspdf": "^2.5.1",
    "html2canvas": "^1.4.1",
    "react-currency-input-field": "^3.6.11"
  }
}
```

## 🗄️ 数据库设计

### 新增数据表

#### 投资计算记录表

```sql
CREATE TABLE investment_calculations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  calculation_type VARCHAR(50) NOT NULL,
  parameters JSONB NOT NULL,
  results JSONB NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  is_favorite BOOLEAN DEFAULT FALSE,
  storage_type VARCHAR(20) DEFAULT 'local', -- 'local', 'cloud', 'synced'
  is_archived BOOLEAN DEFAULT FALSE, -- 用于标记到期用户的数据
  archived_at TIMESTAMP NULL, -- 数据归档时间
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 投资组合对比表

```sql
CREATE TABLE portfolio_comparisons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  calculation_ids UUID[] NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 用户偏好设置表

```sql
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT UNIQUE REFERENCES users(id) ON DELETE CASCADE,
  default_currency VARCHAR(3) DEFAULT 'USD',
  default_calculation_type VARCHAR(50) DEFAULT 'lump_sum',
  chart_preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 数据保留策略表

```sql
CREATE TABLE data_retention_policies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT UNIQUE REFERENCES users(id) ON DELETE CASCADE,
  subscription_status VARCHAR(20) DEFAULT 'free', -- 'free', 'premium', 'expired'
  subscription_expired_at TIMESTAMP NULL,
  data_backup_generated_at TIMESTAMP NULL,
  grace_period_ends_at TIMESTAMP NULL, -- 90天宽限期结束时间
  reminder_sent_at TIMESTAMP NULL, -- 最后一次提醒邮件发送时间
  auto_backup_enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🏗️ 核心模块架构

### 计算引擎 (`src/lib/calculations/`)

```
calculations/
├── types.ts              # 计算参数和结果类型定义
├── engines.ts            # 核心计算逻辑
├── validators.ts          # 参数验证
└── formatters.ts          # 结果格式化
```

### 本地存储管理 (`src/lib/storage/`)

```
storage/
├── local-storage.ts      # 浏览器本地存储管理
├── sync-service.ts       # 云端同步服务
└── export-import.ts      # 数据导入导出
```

### 用户界面架构

#### 主页集成 (`src/components/homepage/`)

```
homepage/
├── hero.tsx                           # 现有Hero组件 - 已修改为投资主题
├── calculator/
│   ├── portfolio-manager.tsx         # 专业投资组合管理器主组件
│   └── portfolio-components.tsx      # 组合管理子组件（表单、视图、图表）
└── features.tsx                      # 现有Features组件 - 需要更新内容
```

#### Dashboard集成 (`src/app/dashboard/`)

```
dashboard/
├── page.tsx                   # 现有Dashboard主页 - 需要修改
├── calculator/
│   ├── page.tsx              # 完整计算器页面
│   ├── components/
│   │   ├── CalculatorForm.tsx      # 计算表单组件
│   │   ├── ResultsDisplay.tsx      # 结果显示组件
│   │   ├── ChartDisplay.tsx        # 图表显示组件
│   │   ├── ComparisonView.tsx      # 对比视图组件
│   │   ├── ExportOptions.tsx       # 导出选项组件
│   │   ├── CurrencySelector.tsx    # 币种选择组件
│   │   └── SavedCalculations.tsx   # 已保存计算列表
│   ├── [type]/
│   │   └── page.tsx               # 特定类型计算器页面
│   └── saved/
│       └── page.tsx               # 已保存的计算管理
└── _components/
    ├── dashboard-page-wrapper.tsx  # 现有组件 - 保持不变
    ├── investment-summary.tsx      # 新增：投资概览卡片
    └── quick-calculator.tsx        # 新增：快速计算器组件
```

#### 共享组件 (`src/components/calculator/`)

```
calculator/
├── core/
│   ├── CalculatorEngine.tsx        # 核心计算逻辑组件
│   ├── InputField.tsx             # 统一输入字段组件
│   ├── ResultCard.tsx             # 结果展示卡片
│   └── ChartWrapper.tsx           # 图表包装组件
├── forms/
│   ├── LumpSumForm.tsx            # 一次性投资表单
│   ├── DCAForm.tsx                # 定投表单
│   ├── RetirementForm.tsx         # 退休规划表单
│   └── SavingsGoalForm.tsx        # 储蓄目标表单
└── charts/
    ├── GrowthChart.tsx            # 增长曲线图
    ├── ComparisonChart.tsx        # 对比图表
    └── BreakdownChart.tsx         # 构成分析图
```

## 🎨 页面集成设计方案

### 主页 (Homepage) 修改方案

#### Hero区域增强

保留现有Hero组件的核心结构，在右侧可视化区域添加简化版投资计算器：

**现有保留元素**：

- 左侧：标题"Build & Launch Your SaaS in Days" → 改为 "Calculate Your Investment Growth"
- 左侧：描述文字和CTA按钮
- 右侧：Dashboard预览卡片的基础布局

**新增元素**：

- 右侧卡片内容替换为简化版计算器
- 包含：初始投资、年化收益率、投资年限三个输入框
- 实时显示计算结果和简单的增长曲线
- "Try Advanced Calculator" 按钮引导到注册/登录

#### Features区域更新

将现有的6个feature卡片内容更新为投资计算器相关功能：

1. **多种计算模式** (替换 Authentication & Users)
2. **实时数据可视化** (替换 Analytics & Insights)
3. **多币种支持** (替换 Payments & Billing)
4. **云端数据同步** (替换 Database & API)
5. **专业报告导出** (替换 Modern UI/UX)
6. **移动端优化** (替换 Production Deploy)

### Dashboard页面修改方案

#### 保留现有Welcome区域

完全保留现有的Welcome Back部分：

```tsx
{
  /* Welcome Section - 保持不变 */
}
<div className="from-primary/5 via-primary/10 to-primary/5 relative overflow-hidden rounded-lg border bg-gradient-to-r p-6">
  <div className="mb-3 flex items-center gap-2">
    <Sparkles className="text-primary h-5 w-5" />
    <Badge variant="secondary" className="text-xs">
      Welcome Back
    </Badge>
  </div>
  <h1 className="text-foreground mb-2 text-2xl font-bold">
    Good to see you again! 👋
  </h1>
  <p className="text-muted-foreground mb-4">
    Here's what's happening with your investment calculations today.
  </p>
</div>;
```

#### 新增投资计算器区域

在Welcome区域下方添加投资计算器功能区域：

**布局结构**：

- 左侧 (2/3 宽度)：完整的投资计算器表单和图表
- 右侧 (1/3 宽度)：已保存的计算列表和快速操作

**功能组件**：

- 计算器类型选择标签页
- 动态表单区域
- 实时结果展示
- 交互式图表
- 保存/分享/导出操作

## 📱 用户体验设计

### 响应式设计

- **移动端优先** - 确保在手机上的良好体验
- **触摸友好** - 大按钮、易点击的交互元素
- **自适应布局** - 图表和表格在不同屏幕尺寸下的最佳显示

### 交互体验

- **实时计算** - 参数变化时立即更新结果
- **渐进式表单** - 分步骤引导用户输入
- **智能默认值** - 基于用户历史偏好设置默认参数
- **快捷操作** - 一键复制、分享、保存功能

### 可访问性

- **键盘导航** - 完整的键盘操作支持
- **屏幕阅读器** - 语义化的 HTML 结构
- **高对比度** - 支持高对比度主题
- **多语言** - 预留国际化支持

## 🚀 实施计划

### 第一阶段：基础架构和主页集成 (2-3周)

- [ ] 数据库模式设计和迁移
- [ ] 核心计算引擎开发
- [ ] 基础 UI 组件开发
- [ ] **主页Hero区域修改** - 添加简化版计算器
- [ ] **主页Features区域更新** - 更新为投资计算器功能
- [ ] 本地存储功能实现

### 第二阶段：Dashboard集成和核心功能 (2-3周)

- [ ] **Dashboard页面修改** - 保留Welcome区域，添加计算器功能
- [ ] 四种计算模式实现
- [ ] 图表和数据可视化集成
- [ ] 多币种支持
- [ ] 计算历史管理
- [ ] **响应式布局优化** - 确保移动端体验

### 第三阶段：高级功能和用户体验 (2-3周)

- [ ] 投资组合对比功能
- [ ] PDF 报告生成
- [ ] 数据导入导出
- [ ] 云端同步服务
- [ ] **主页到Dashboard的用户流程优化**
- [ ] **免费版功能限制实现**

### 第四阶段：订阅集成和权限控制 (1-2周)

- [ ] 功能权限控制 (免费版 vs 付费版)
- [ ] 订阅升级流程
- [ ] 支付集成测试
- [ ] **主页CTA按钮优化** - 引导用户注册/升级
- [ ] 用户体验优化

### 第五阶段：测试部署和优化 (1周)

- [ ] 全面功能测试
- [ ] **主页和Dashboard集成测试**
- [ ] 性能优化
- [ ] 安全性检查
- [ ] 生产环境部署

## 💡 商业模式

### 免费增值策略

- **完全免费使用** - 无需注册即可使用基础功能
- **价值体验** - 让用户先体验到产品价值
- **自然升级** - 当用户需要更多功能时自然引导付费

### 订阅定价

- **月度订阅**: $4.49/月
- **年度订阅**：$38.88/年

## 🎨 设计原则

### 简洁直观

- **最小化认知负担** - 清晰的信息层次
- **一目了然** - 重要信息突出显示
- **渐进式披露** - 高级功能不干扰基础使用

### 专业可信

- **数据准确性** - 使用高精度计算库
- **透明计算** - 显示计算公式和假设
- **专业报告** - 银行级别的报告质量

### 用户友好

- **零学习成本** - 直观的操作流程
- **智能提示** - 适时的帮助和建议
- **错误容忍** - 友好的错误处理和恢复

## 📈 成功指标

### 用户参与度

- **日活跃用户** - 目标 1000+ DAU
- **计算完成率** - 目标 85%+
- **功能使用率** - 各功能模块的使用分布

### 商业指标

- **转化率** - 免费用户到付费用户转化率 5%+
- **用户留存** - 30天留存率 40%+
- **客户生命周期价值** - LTV/CAC > 3

### 技术指标

- **页面加载速度** - 首屏加载 < 2秒
- **计算响应时间** - < 100ms
- **系统可用性** - 99.9%+

## 🔧 技术实现指南

### 主页集成具体步骤

#### 1. 修改Hero组件 (`src/components/homepage/hero.tsx`)

```tsx
// 保留现有左侧内容，修改标题和描述
<h1 className="text-foreground text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
  Calculate Your
  <span className="text-primary block">Investment Growth</span>
</h1>

// 右侧添加简化版计算器
<div className="border-border bg-background/50 relative mr-6 rounded-xl border p-6 shadow-xl backdrop-blur-sm">
  <InvestmentCalculatorPreview />
</div>
```

#### 2. 创建简化版计算器组件 (`src/components/homepage/investment-calculator.tsx`)

```tsx
export function InvestmentCalculatorPreview() {
  const [amount, setAmount] = useState(10000);
  const [rate, setRate] = useState(7);
  const [years, setYears] = useState(10);

  const result = calculateCompoundInterest(amount, rate, years);

  return (
    <div className="space-y-4">
      <h3 className="font-semibold">Quick Calculator</h3>
      {/* 三个输入框 */}
      {/* 结果显示 */}
      {/* 简单图表 */}
      <Button asChild>
        <Link href="/signup">Try Advanced Calculator</Link>
      </Button>
    </div>
  );
}
```

#### 3. 更新Features组件内容 (`src/components/homepage/features.tsx`)

```tsx
const features: Feature[] = [
  {
    title: "Multiple Calculation Types",
    description:
      "Lump sum, DCA, retirement planning, and savings goal calculators.",
    icon: Calculator,
    category: "Tools",
  },
  // ... 其他5个功能
];
```

### Dashboard集成具体步骤

#### 1. 修改Dashboard主页 (`src/app/dashboard/page.tsx`)

```tsx
export default function HomeRoute() {
  return (
    <DashboardPageWrapper title="Investment Calculator">
      {/* 保留现有Welcome Section */}
      <WelcomeSection />

      {/* 新增投资计算器区域 */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <InvestmentCalculatorMain />
        </div>
        <div className="space-y-6">
          <SavedCalculationsList />
          <QuickActions />
        </div>
      </div>
    </DashboardPageWrapper>
  );
}
```

#### 2. 创建主要计算器组件 (`src/app/dashboard/_components/investment-calculator-main.tsx`)

```tsx
export function InvestmentCalculatorMain() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Investment Calculator</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="lump-sum">
          <TabsList>
            <TabsTrigger value="lump-sum">Lump Sum</TabsTrigger>
            <TabsTrigger value="dca">DCA</TabsTrigger>
            <TabsTrigger value="retirement">Retirement</TabsTrigger>
            <TabsTrigger value="savings">Savings Goal</TabsTrigger>
          </TabsList>

          <TabsContent value="lump-sum">
            <LumpSumCalculator />
          </TabsContent>
          {/* 其他标签页内容 */}
        </Tabs>
      </CardContent>
    </Card>
  );
}
```

### 路由和导航更新

#### 1. 更新侧边栏导航 (`src/app/dashboard/_components/app-sidebar.tsx`)

```tsx
const navigationItems = [
  {
    title: "Calculator",
    url: "/dashboard",
    icon: Calculator,
  },
  {
    title: "Saved Calculations",
    url: "/dashboard/calculator/saved",
    icon: BookmarkIcon,
  },
  // ... 其他导航项
];
```

#### 2. 更新主页导航 (`src/components/homepage/header.tsx`)

```tsx
const navigationItems: NavItem[] = [
  {
    title: "Calculator",
    href: "/dashboard",
    description: "Investment growth calculator and planning tools",
  },
  // ... 其他导航项
];
```

### 数据迁移和保留实现

#### 1. 数据迁移服务 (`src/lib/data-migration/migration-service.ts`)

```tsx
export class DataMigrationService {
  // 免费用户升级时的数据迁移
  async migrateLocalToCloud(userId: string) {
    const localData = await this.getLocalCalculations();
    const cloudData = await this.getCloudCalculations(userId);

    // 智能合并数据，避免重复
    const mergedData = this.mergeCalculations(localData, cloudData);

    // 同步到云端
    await this.syncToCloud(userId, mergedData);

    // 更新本地存储状态
    await this.updateLocalStorageStatus("synced");
  }

  // 订阅到期时的数据备份
  async createSubscriptionExpiryBackup(userId: string) {
    const cloudData = await this.getCloudCalculations(userId);

    // 生成备份文件
    const backupData = {
      calculations: cloudData,
      exportedAt: new Date().toISOString(),
      userId: userId,
    };

    // 保存到本地存储
    await this.saveToLocalStorage(backupData);

    // 发送备份下载链接给用户
    await this.sendBackupDownloadEmail(userId, backupData);
  }

  // 重新订阅时恢复数据
  async restoreSubscriptionData(userId: string) {
    // 检查是否有备份数据
    const backupData = await this.getBackupData(userId);

    if (backupData) {
      // 恢复云端同步
      await this.restoreCloudSync(userId, backupData);

      // 更新订阅状态
      await this.updateSubscriptionStatus(userId, "premium");
    }
  }
}
```

#### 2. 数据保留策略服务 (`src/lib/data-retention/retention-service.ts`)

```tsx
export class DataRetentionService {
  // 检查订阅状态并执行相应策略
  async checkAndApplyRetentionPolicy(userId: string) {
    const policy = await this.getRetentionPolicy(userId);

    if (policy.subscription_status === "expired") {
      const daysSinceExpiry = this.getDaysSince(policy.subscription_expired_at);

      if (daysSinceExpiry >= 90) {
        // 90天宽限期结束，归档数据
        await this.archiveUserData(userId);
      } else if (daysSinceExpiry >= 83) {
        // 最后一周，发送紧急提醒
        await this.sendUrgentReminderEmail(userId);
      }
    }
  }

  // 发送到期提醒邮件
  async sendExpiryReminders() {
    const expiringUsers = await this.getExpiringUsers();

    for (const user of expiringUsers) {
      const daysUntilExpiry = this.getDaysUntil(user.subscription_expires_at);

      if (daysUntilExpiry === 7 || daysUntilExpiry === 1) {
        await this.sendReminderEmail(user.id, daysUntilExpiry);
      }
    }
  }
}
```

---

_本文档将随着项目进展持续更新和完善。_
