import { StoredCalculation } from "../calculations/types";

export interface StorageQuota {
  used: number;
  available: number;
  total: number;
}

export class LocalStorageService {
  private static readonly STORAGE_KEY = "investment_calculations";
  private static readonly PREFERENCES_KEY = "investment_preferences";
  private static readonly MAX_FREE_CALCULATIONS = 3;
  private static readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5MB

  // 获取所有计算记录
  static getCalculations(): StoredCalculation[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const data = JSON.parse(stored);

      // 验证数据格式
      if (!Array.isArray(data)) {
        console.warn("Invalid calculations data format, resetting...");
        this.setCalculations([]);
        return [];
      }

      return data;
    } catch (error) {
      console.error("Error reading calculations from localStorage:", error);
      return [];
    }
  }

  // 保存新的计算记录
  static saveCalculation(
    calculation: Omit<StoredCalculation, "id" | "createdAt" | "updatedAt">,
  ): string {
    const calculations = this.getCalculations();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const newCalculation: StoredCalculation = {
      ...calculation,
      id,
      createdAt: now,
      updatedAt: now,
    };

    // 检查免费版限制
    if (
      !this.isPremiumUser() &&
      calculations.length >= this.MAX_FREE_CALCULATIONS
    ) {
      throw new Error(
        `Free version limited to ${this.MAX_FREE_CALCULATIONS} saved calculations. Upgrade to save more.`,
      );
    }

    // 检查存储空间
    const updatedCalculations = [...calculations, newCalculation];
    if (!this.checkStorageSpace(updatedCalculations)) {
      throw new Error(
        "Storage space exceeded. Please delete some calculations or upgrade.",
      );
    }

    this.setCalculations(updatedCalculations);

    return id;
  }

  // 更新计算记录
  static updateCalculation(
    id: string,
    updates: Partial<StoredCalculation>,
  ): void {
    const calculations = this.getCalculations();
    const index = calculations.findIndex((calc) => calc.id === id);

    if (index === -1) {
      throw new Error("Calculation not found");
    }

    calculations[index] = {
      ...calculations[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.setCalculations(calculations);
  }

  // 删除计算记录
  static deleteCalculation(id: string): void {
    const calculations = this.getCalculations();
    const filtered = calculations.filter((calc) => calc.id !== id);

    if (filtered.length === calculations.length) {
      throw new Error("Calculation not found");
    }

    this.setCalculations(filtered);
  }

  // 批量删除计算记录
  static deleteCalculations(ids: string[]): void {
    const calculations = this.getCalculations();
    const filtered = calculations.filter((calc) => !ids.includes(calc.id));
    this.setCalculations(filtered);
  }

  // 获取单个计算记录
  static getCalculation(id: string): StoredCalculation | null {
    const calculations = this.getCalculations();
    return calculations.find((calc) => calc.id === id) || null;
  }

  // 获取收藏的计算记录
  static getFavoriteCalculations(): StoredCalculation[] {
    return this.getCalculations().filter((calc) => calc.isFavorite);
  }

  // 按类型获取计算记录
  static getCalculationsByType(type: string): StoredCalculation[] {
    return this.getCalculations().filter((calc) => calc.type === type);
  }

  // 搜索计算记录
  static searchCalculations(query: string): StoredCalculation[] {
    const calculations = this.getCalculations();
    const lowercaseQuery = query.toLowerCase();

    return calculations.filter(
      (calc) =>
        calc.name.toLowerCase().includes(lowercaseQuery) ||
        calc.type.toLowerCase().includes(lowercaseQuery),
    );
  }

  // 设置计算记录（私有方法）
  private static setCalculations(calculations: StoredCalculation[]): void {
    try {
      const data = JSON.stringify(calculations);
      localStorage.setItem(this.STORAGE_KEY, data);
    } catch (error) {
      console.error("Error writing to localStorage:", error);

      if (error instanceof DOMException && error.code === 22) {
        throw new Error(
          "Storage quota exceeded. Please delete some calculations.",
        );
      }

      throw new Error("Failed to save calculation");
    }
  }

  // 检查是否为付费用户（临时实现）
  private static isPremiumUser(): boolean {
    // TODO: 与认证系统集成
    // 这里需要检查用户的订阅状态
    return false;
  }

  // 检查存储空间
  private static checkStorageSpace(data: StoredCalculation[]): boolean {
    try {
      const serialized = JSON.stringify(data);
      const sizeInBytes = new Blob([serialized]).size;
      return sizeInBytes <= this.MAX_STORAGE_SIZE;
    } catch (error) {
      console.error("Error checking storage space:", error);
      return false;
    }
  }

  // 获取存储使用情况
  static getStorageQuota(): StorageQuota {
    try {
      const calculations = this.getCalculations();
      const serialized = JSON.stringify(calculations);
      const used = new Blob([serialized]).size;

      return {
        used,
        available: this.MAX_STORAGE_SIZE - used,
        total: this.MAX_STORAGE_SIZE,
      };
    } catch (error) {
      console.error("Error getting storage quota:", error);
      return {
        used: 0,
        available: this.MAX_STORAGE_SIZE,
        total: this.MAX_STORAGE_SIZE,
      };
    }
  }

  // 导出数据
  static exportData(): string {
    const calculations = this.getCalculations();
    const preferences = this.getPreferences();

    const exportData = {
      version: "1.0",
      exportedAt: new Date().toISOString(),
      calculations,
      preferences,
      metadata: {
        totalCalculations: calculations.length,
        favoriteCalculations: calculations.filter((c) => c.isFavorite).length,
        calculationTypes: Array.from(new Set(calculations.map((c) => c.type))),
      },
    };

    return JSON.stringify(exportData, null, 2);
  }

  // 导入数据
  static importData(jsonData: string): void {
    try {
      const importData = JSON.parse(jsonData);

      // 验证数据格式
      if (!importData.calculations || !Array.isArray(importData.calculations)) {
        throw new Error(
          "Invalid import data format: missing calculations array",
        );
      }

      // 验证每个计算记录的结构
      for (const calc of importData.calculations) {
        if (!calc.id || !calc.type || !calc.parameters || !calc.results) {
          throw new Error("Invalid calculation data in import");
        }
      }

      // 检查存储空间
      if (!this.checkStorageSpace(importData.calculations)) {
        throw new Error("Import data exceeds storage limit");
      }

      // 导入计算记录
      this.setCalculations(importData.calculations);

      // 导入用户偏好（如果存在）
      if (importData.preferences) {
        this.setPreferences(importData.preferences);
      }
    } catch (error) {
      console.error("Error importing data:", error);
      throw new Error("Failed to import data. Please check the file format.");
    }
  }

  // 清空所有数据
  static clearAllData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.PREFERENCES_KEY);
    } catch (error) {
      console.error("Error clearing data:", error);
      throw new Error("Failed to clear data");
    }
  }

  // 用户偏好管理
  static getPreferences(): Record<string, unknown> {
    try {
      const stored = localStorage.getItem(this.PREFERENCES_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error("Error reading preferences:", error);
      return {};
    }
  }

  static setPreferences(preferences: Record<string, unknown>): void {
    try {
      localStorage.setItem(this.PREFERENCES_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.error("Error saving preferences:", error);
      throw new Error("Failed to save preferences");
    }
  }

  // 获取统计信息
  static getStatistics(): Record<string, unknown> {
    const calculations = this.getCalculations();
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const recentCalculations = calculations.filter(
      (calc) => new Date(calc.createdAt) >= thirtyDaysAgo,
    );

    const typeStats = calculations.reduce(
      (acc, calc) => {
        acc[calc.type] = (acc[calc.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return {
      total: calculations.length,
      favorites: calculations.filter((c) => c.isFavorite).length,
      recentCount: recentCalculations.length,
      typeBreakdown: typeStats,
      storageQuota: this.getStorageQuota(),
    };
  }
}
