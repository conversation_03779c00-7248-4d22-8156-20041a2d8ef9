import { authSchema } from "./auth.schema";

describe("authSchema", () => {
  describe("email validation", () => {
    it("should accept valid email addresses", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach((email) => {
        const result = authSchema.safeParse({ email });
        expect(result.success).toBe(true);
      });
    });

    it("should reject invalid email addresses", () => {
      const invalidEmails = [
        "invalid-email",
        "@example.com",
        "user@",
        "<EMAIL>",
        "",
      ];

      invalidEmails.forEach((email) => {
        const result = authSchema.safeParse({ email });
        expect(result.success).toBe(false);
      });
    });

    it("should trim whitespace from email addresses", () => {
      const emailsWithWhitespace = [
        "  <EMAIL>  ",
        "\<EMAIL>\t",
        "\<EMAIL>\n",
        " <EMAIL>",
        "<EMAIL> ",
      ];

      emailsWithWhitespace.forEach((email) => {
        const result = authSchema.safeParse({ email });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.email).toBe("<EMAIL>");
        }
      });
    });

    it("should normalize email addresses to lowercase", () => {
      const emailsWithMixedCase = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      emailsWithMixedCase.forEach((email) => {
        const result = authSchema.safeParse({ email });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.email).toBe(email.toLowerCase());
        }
      });
    });

    it("should handle combined whitespace and case normalization", () => {
      const testCases = [
        { input: "  <EMAIL>  ", expected: "<EMAIL>" },
        { input: "\<EMAIL>\t", expected: "<EMAIL>" },
        { input: " <EMAIL> ", expected: "<EMAIL>" },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = authSchema.safeParse({ email: input });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.email).toBe(expected);
        }
      });
    });

    it("should return appropriate error message for invalid emails", () => {
      const result = authSchema.safeParse({ email: "invalid-email" });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "Please enter a valid email address",
        );
      }
    });
  });
});
