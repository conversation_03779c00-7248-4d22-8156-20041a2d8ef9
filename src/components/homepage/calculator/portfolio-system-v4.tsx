"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Calculator,
  TrendingUp,
  DollarSign,
  PieChart,
  Plus,
  Edit2,
  Trash2,
  Eye,
  EyeOff,
  Sparkles,
  ArrowRight,
  BarChart3,
  Calendar,
  Target,
  Briefcase,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import Link from "next/link";
import {
  <PERSON><PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency, CurrencyUtils } from "@/lib/utils/currency";
import { PercentageInputField } from "@/components/calculator/core/InputField";
import { PortfolioForm, HoldingForm } from "./portfolio-forms-v2";
import {
  OverviewView,
  HoldingsPoolView,
  ManagePortfolioView,
} from "./portfolio-views-v4";
import type { SupportedCurrency } from "@/lib/calculations/types";

// 重新设计的数据结构
interface InvestmentRecord {
  id: string;
  date: Date;
  amount: number;
  price?: number;
  shares?: number;
  fees?: number;
  note?: string;
  type: "buy" | "sell" | "dividend";
}

// 全局投资标的（可以被多个组合引用）
interface GlobalHolding {
  id: string;
  name: string;
  symbol?: string;
  type: "stock" | "etf" | "mutual_fund" | "bond" | "crypto" | "other";
  currency: SupportedCurrency;
  color: string;
  records: InvestmentRecord[];
  // 计算属性
  totalInvested: number;
  currentValue: number;
  totalShares: number;
  averageCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
}

// 组合中的标的配置
interface PortfolioHolding {
  holdingId: string; // 引用GlobalHolding的ID
  targetAllocation: number; // 在该组合中的目标配置比例
  isVisible: boolean; // 在该组合图表中是否可见
}

// 投资组合
interface Portfolio {
  id: string;
  name: string;
  description: string;
  strategy: string;
  riskLevel: "conservative" | "moderate" | "aggressive";
  currency: SupportedCurrency;
  holdings: PortfolioHolding[]; // 引用全局标的
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// 预定义颜色
const HOLDING_COLORS = [
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#ec4899",
  "#6366f1",
];

// 免费版限制
const FREE_TIER_LIMITS = {
  maxPortfolios: 1,
  maxHoldingsPerPortfolio: 3,
  maxGlobalHoldings: 5,
  maxRecordsPerHolding: 10,
};

export function PortfolioSystemV4() {
  // 全局投资标的池
  const [globalHoldings, setGlobalHoldings] = useState<GlobalHolding[]>([
    {
      id: "h1",
      name: "Vanguard S&P 500 ETF",
      symbol: "VOO",
      type: "etf",
      currency: "USD",
      color: HOLDING_COLORS[0],
      totalInvested: 10000,
      currentValue: 12500,
      totalShares: 25.5,
      averageCost: 392.16,
      unrealizedGain: 2500,
      unrealizedGainPercent: 25,
      records: [
        {
          id: "r1",
          date: new Date("2024-01-15"),
          amount: 5000,
          price: 400,
          shares: 12.5,
          fees: 0,
          note: "初始投资",
          type: "buy",
        },
        {
          id: "r2",
          date: new Date("2024-02-15"),
          amount: 2000,
          price: 410,
          shares: 4.88,
          fees: 0,
          note: "定期投资",
          type: "buy",
        },
        {
          id: "r3",
          date: new Date("2024-03-15"),
          amount: 3000,
          price: 380,
          shares: 7.89,
          fees: 0,
          note: "逢低加仓",
          type: "buy",
        },
      ],
    },
    {
      id: "h2",
      name: "Vanguard Total Bond Market ETF",
      symbol: "BND",
      type: "etf",
      currency: "USD",
      color: HOLDING_COLORS[1],
      totalInvested: 8000,
      currentValue: 8200,
      totalShares: 100,
      averageCost: 80,
      unrealizedGain: 200,
      unrealizedGainPercent: 2.5,
      records: [
        {
          id: "r4",
          date: new Date("2024-01-15"),
          amount: 8000,
          price: 80,
          shares: 100,
          fees: 0,
          note: "一次性投资债券",
          type: "buy",
        },
      ],
    },
  ]);

  // 投资组合
  const [portfolios, setPortfolios] = useState<Portfolio[]>([
    {
      id: "1",
      name: "我的土豆沙发组合",
      description: "简单的被动投资策略",
      strategy: "60% 股票 + 40% 债券",
      riskLevel: "moderate",
      currency: "USD",
      holdings: [
        { holdingId: "h1", targetAllocation: 60, isVisible: true },
        { holdingId: "h2", targetAllocation: 40, isVisible: true },
      ],
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date(),
      isActive: true,
    },
  ]);

  const [activePortfolio, setActivePortfolio] = useState<string>("1");
  const [viewMode, setViewMode] = useState<"overview" | "holdings" | "manage">(
    "overview",
  );

  // 编辑状态
  const [editingPortfolio, setEditingPortfolio] = useState<Portfolio | null>(
    null,
  );
  const [editingHolding, setEditingHolding] = useState<GlobalHolding | null>(
    null,
  );
  const [isAddPortfolioOpen, setIsAddPortfolioOpen] = useState(false);
  const [isAddHoldingOpen, setIsAddHoldingOpen] = useState(false);
  const [isManagePortfolioHoldingsOpen, setIsManagePortfolioHoldingsOpen] =
    useState(false);

  // 获取当前组合
  const currentPortfolio = portfolios.find((p) => p.id === activePortfolio);

  // 添加全局投资标的
  const addGlobalHolding = (holdingData: Partial<GlobalHolding>) => {
    if (globalHoldings.length >= FREE_TIER_LIMITS.maxGlobalHoldings) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxGlobalHoldings} global holdings. Upgrade for unlimited holdings.`,
      );
      return;
    }

    const newHolding: GlobalHolding = {
      id: Date.now().toString(),
      name: holdingData.name || "新投资标的",
      symbol: holdingData.symbol,
      type: holdingData.type || "etf",
      currency: holdingData.currency || "USD",
      color: HOLDING_COLORS[globalHoldings.length % HOLDING_COLORS.length],
      records: [],
      totalInvested: 0,
      currentValue: 0,
      totalShares: 0,
      averageCost: 0,
      unrealizedGain: 0,
      unrealizedGainPercent: 0,
    };

    setGlobalHoldings([...globalHoldings, newHolding]);
    setIsAddHoldingOpen(false);
  };

  // 更新全局投资标的
  const updateGlobalHolding = (
    holdingId: string,
    updates: Partial<GlobalHolding>,
  ) => {
    setGlobalHoldings(
      globalHoldings.map((h) =>
        h.id === holdingId ? { ...h, ...updates } : h,
      ),
    );
    setEditingHolding(null);
  };

  // 添加投资记录到全局标的
  const addRecordToGlobalHolding = (
    holdingId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    const newRecord: InvestmentRecord = {
      id: Date.now().toString(),
      date: recordData.date || new Date(),
      amount: recordData.amount || 0,
      price: recordData.price,
      shares: recordData.shares,
      fees: recordData.fees,
      note: recordData.note,
      type: recordData.type || "buy",
    };

    setGlobalHoldings(
      globalHoldings.map((h) =>
        h.id === holdingId
          ? {
              ...h,
              records: [...h.records, newRecord],
              // 重新计算统计数据
              totalInvested: h.totalInvested + newRecord.amount,
              totalShares: h.totalShares + (newRecord.shares || 0),
            }
          : h,
      ),
    );
  };

  // 更新投资记录
  const updateRecordInGlobalHolding = (
    holdingId: string,
    recordId: string,
    recordData: Partial<InvestmentRecord>,
  ) => {
    setGlobalHoldings(
      globalHoldings.map((h) =>
        h.id === holdingId
          ? {
              ...h,
              records: h.records.map((r) =>
                r.id === recordId ? { ...r, ...recordData } : r,
              ),
            }
          : h,
      ),
    );
  };

  // 删除投资记录
  const deleteRecordFromGlobalHolding = (
    holdingId: string,
    recordId: string,
  ) => {
    setGlobalHoldings(
      globalHoldings.map((h) =>
        h.id === holdingId
          ? {
              ...h,
              records: h.records.filter((r) => r.id !== recordId),
            }
          : h,
      ),
    );
  };

  // 添加投资组合
  const addPortfolio = (portfolioData: Partial<Portfolio>) => {
    if (portfolios.length >= FREE_TIER_LIMITS.maxPortfolios) {
      alert(
        `Free tier is limited to ${FREE_TIER_LIMITS.maxPortfolios} portfolio. Upgrade for unlimited portfolios.`,
      );
      return;
    }

    const newPortfolio: Portfolio = {
      id: Date.now().toString(),
      name: portfolioData.name || "新投资组合",
      description: portfolioData.description || "",
      strategy: portfolioData.strategy || "",
      riskLevel: portfolioData.riskLevel || "moderate",
      currency: portfolioData.currency || "USD",
      holdings: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
    };

    setPortfolios([...portfolios, newPortfolio]);
    setActivePortfolio(newPortfolio.id);
    setIsAddPortfolioOpen(false);
  };

  // 更新投资组合
  const updatePortfolio = (
    portfolioId: string,
    updates: Partial<Portfolio>,
  ) => {
    setPortfolios(
      portfolios.map((p) =>
        p.id === portfolioId ? { ...p, ...updates, updatedAt: new Date() } : p,
      ),
    );
    setEditingPortfolio(null);
  };

  // 获取组合的完整标的信息
  const getPortfolioHoldingsWithData = (portfolio: Portfolio) => {
    return portfolio.holdings
      .map((ph) => {
        const globalHolding = globalHoldings.find(
          (gh) => gh.id === ph.holdingId,
        );
        return {
          ...ph,
          ...globalHolding,
          targetAllocation: ph.targetAllocation,
          isVisible: ph.isVisible,
        };
      })
      .filter((h) => h.name); // 过滤掉找不到的标的
  };

  // 基于真实投资记录生成图表数据
  const generateRealChartData = (holdings: any[]) => {
    const allDates = new Set<string>();

    // 收集所有投资日期
    holdings.forEach((holding) => {
      holding.records?.forEach((record: InvestmentRecord) => {
        allDates.add(record.date.toISOString().split("T")[0]);
      });
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 为每个日期计算累积价值
    const chartData = sortedDates.map((dateStr) => {
      const date = new Date(dateStr);
      const dataPoint: any = {
        date: date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
      };

      holdings.forEach((holding) => {
        // 计算到该日期为止的累积投资
        const recordsUpToDate =
          holding.records?.filter(
            (r: InvestmentRecord) => new Date(r.date) <= date,
          ) || [];

        const cumulativeInvestment = recordsUpToDate.reduce(
          (sum: number, r: InvestmentRecord) => sum + r.amount,
          0,
        );

        // 简单模拟：假设从投资日期开始有一定增长
        const daysSinceStart =
          recordsUpToDate.length > 0
            ? Math.max(
                1,
                (date.getTime() - new Date(recordsUpToDate[0].date).getTime()) /
                  (1000 * 60 * 60 * 24),
              )
            : 0;

        // 模拟年化8%的增长
        const growthFactor = Math.pow(1.08, daysSinceStart / 365);
        dataPoint[holding.symbol || holding.name] =
          cumulativeInvestment * growthFactor;
      });

      return dataPoint;
    });

    return chartData;
  };

  return (
    <section id="portfolio-system" className="bg-background py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto mb-16 max-w-2xl text-center">
          <Badge variant="secondary" className="mb-4">
            <Briefcase className="mr-2 h-3 w-3" />
            Professional Portfolio Management
          </Badge>

          <h2 className="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
            Build Your Investment Portfolio
          </h2>

          <p className="text-muted-foreground mt-4 text-lg">
            Create portfolios from your investment holdings pool. Track real
            investments and analyze performance with data-driven charts.
          </p>
        </div>

        {/* Portfolio Selector & Controls */}
        <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-4">
            {/* Portfolio Selector */}
            <Select value={activePortfolio} onValueChange={setActivePortfolio}>
              <SelectTrigger className="w-64">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {portfolios.map((portfolio) => (
                  <SelectItem key={portfolio.id} value={portfolio.id}>
                    {portfolio.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* View Mode Tabs */}
            <div className="flex rounded-lg border">
              <Button
                variant={viewMode === "overview" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("overview")}
              >
                Overview
              </Button>
              <Button
                variant={viewMode === "holdings" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("holdings")}
              >
                Holdings Pool
              </Button>
              <Button
                variant={viewMode === "manage" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("manage")}
              >
                Manage Portfolio
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddPortfolioOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              New Portfolio
            </Button>
            <Button size="sm" onClick={() => setIsAddHoldingOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Holding
            </Button>
          </div>
        </div>

        {/* Main Content */}
        {currentPortfolio && (
          <>
            {viewMode === "overview" && (
              <OverviewView
                portfolio={currentPortfolio}
                holdingsWithData={getPortfolioHoldingsWithData(
                  currentPortfolio,
                )}
                chartData={generateRealChartData(
                  getPortfolioHoldingsWithData(currentPortfolio),
                )}
                onEditPortfolio={setEditingPortfolio}
              />
            )}

            {viewMode === "holdings" && (
              <HoldingsPoolView
                globalHoldings={globalHoldings}
                onEditHolding={setEditingHolding}
                onDeleteHolding={(id) =>
                  setGlobalHoldings((prev) => prev.filter((h) => h.id !== id))
                }
                onAddRecord={addRecordToGlobalHolding}
                onUpdateRecord={updateRecordInGlobalHolding}
                onDeleteRecord={deleteRecordFromGlobalHolding}
              />
            )}

            {viewMode === "manage" && (
              <ManagePortfolioView
                portfolio={currentPortfolio}
                globalHoldings={globalHoldings}
                onUpdatePortfolio={(updates) =>
                  updatePortfolio(currentPortfolio.id, updates)
                }
              />
            )}
          </>
        )}

        {/* Edit Dialogs */}
        {editingPortfolio && (
          <Dialog
            open={!!editingPortfolio}
            onOpenChange={() => setEditingPortfolio(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Portfolio</DialogTitle>
              </DialogHeader>
              <PortfolioForm
                initialData={editingPortfolio}
                onSave={(updates) =>
                  updatePortfolio(editingPortfolio.id, updates)
                }
              />
            </DialogContent>
          </Dialog>
        )}

        {editingHolding && (
          <Dialog
            open={!!editingHolding}
            onOpenChange={() => setEditingHolding(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Holding</DialogTitle>
              </DialogHeader>
              <HoldingForm
                initialData={editingHolding}
                onSave={(updates) =>
                  updateGlobalHolding(editingHolding.id, updates)
                }
                currency="USD"
              />
            </DialogContent>
          </Dialog>
        )}

        <Dialog open={isAddPortfolioOpen} onOpenChange={setIsAddPortfolioOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Portfolio</DialogTitle>
              <DialogDescription>
                Create a new investment portfolio. You can add holdings after
                creating the portfolio.
              </DialogDescription>
            </DialogHeader>
            <PortfolioForm onSave={addPortfolio} />
          </DialogContent>
        </Dialog>

        <Dialog open={isAddHoldingOpen} onOpenChange={setIsAddHoldingOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Investment Holding</DialogTitle>
              <DialogDescription>
                Add a new investment holding to your global holdings pool.
              </DialogDescription>
            </DialogHeader>
            <HoldingForm
              onSave={addGlobalHolding}
              currency="USD"
              isDisabled={
                globalHoldings.length >= FREE_TIER_LIMITS.maxGlobalHoldings
              }
            />
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
}
