{"permissions": {"allow": ["Bash(cp:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(pnpm dev:*)", "Bash(pnpm build:*)", "Bash(pnpm start:*)", "Bash(pnpm stop:*)", "Bash(pnpm restart:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm test)", "Bash(npm run:*)", "Bash(npm run:*:*)", "Bash(pnpm test)", "Bash(npm test:*)", "Bash(pnpm test:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(pnpm lint:*)", "Bash(pnpm run lint:*)", "Bash(pnpm run:*)", "Bash(pnpm tsc:*)", "Bash(grep:*)", "Bash(rm:*)", "<PERSON><PERSON>(jest --coverage)", "<PERSON><PERSON>(npx jest:*)", "Bash(ls:*)", "Bash(git rm:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mv:*)", "Bash(pnpm list:*)", "Bash(pnpm add:*)", "Bash(pnpm install:*)", "Bash(node:*)", "Bash(pnpm build:*)", "Bash(pnpm test:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(sed:*)"], "deny": []}}