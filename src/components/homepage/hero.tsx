import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  CheckCircle,
  Calculator,
  Sparkles,
  DollarSign,
  <PERSON><PERSON>hart,
  BarChart3,
} from "lucide-react";
import { APP_DESCRIPTION } from "@/lib/config/constants";
import Link from "next/link";

export function Hero() {
  return (
    <section className="bg-background relative flex items-center justify-center overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.03),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(to_right,hsl(var(--border))_1px,transparent_1px),linear-gradient(to_bottom,hsl(var(--border))_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-20" />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 py-16 lg:px-8 lg:py-24">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Status Badge */}
            <div className="border-border bg-background/50 inline-flex items-center rounded-full border px-3 py-1 text-sm backdrop-blur-sm">
              <Sparkles className="text-primary mr-2 h-3 w-3" />
              <span className="text-muted-foreground">
                Professional Investment Calculator
              </span>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-foreground text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
                Calculate Your Investment
                <span className="text-primary block">Growth Potential</span>
              </h1>

              <p className="text-muted-foreground max-w-lg text-xl leading-relaxed">
                Plan your financial future with our powerful investment
                calculator. Visualize compound growth, compare strategies, and
                make informed decisions.
              </p>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap gap-6 text-sm">
              <div className="text-muted-foreground flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-emerald-500" />
                <span>Multiple Strategies</span>
              </div>
              <div className="text-muted-foreground flex items-center gap-2">
                <Calculator className="h-4 w-4 text-blue-500" />
                <span>Accurate Calculations</span>
              </div>
              <div className="text-muted-foreground flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span>Growth Visualization</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row">
              <Button
                size="lg"
                className="group h-12 px-8 text-base font-medium"
                asChild
              >
                <Link href="/signup">
                  Start Calculating
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="group border-border hover:bg-accent h-12 px-8 text-base font-medium"
                asChild
              >
                <Link href="#calculator">
                  <Calculator className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" />
                  Try Free Calculator
                </Link>
              </Button>
            </div>

            {/* Additional Info */}
            <div className="text-muted-foreground flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-500" />
                <span>Multiple calculation modes</span>
              </div>
              <div className="bg-border h-4 w-px" />
              <div className="flex items-center gap-2">
                <PieChart className="h-4 w-4 text-purple-500" />
                <span>Visual growth charts</span>
              </div>
            </div>
          </div>

          {/* Right Visual */}
          <div className="relative lg:order-last">
            <div className="relative mx-auto max-w-lg">
              {/* Main Investment Calculator Preview */}
              <div className="border-border bg-background/50 relative mr-6 rounded-xl border p-6 shadow-xl backdrop-blur-sm">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
                        <DollarSign className="text-primary h-4 w-4" />
                      </div>
                      <div>
                        <div className="text-foreground text-sm font-medium">
                          Investment Portfolio
                        </div>
                        <div className="text-muted-foreground text-xs">
                          10 Year Growth
                        </div>
                      </div>
                    </div>
                    <div className="rounded-full bg-emerald-500/20 px-2 py-1 text-xs font-medium text-emerald-600">
                      +127%
                    </div>
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="border-border/50 bg-background/30 rounded-lg border p-3">
                      <div className="text-muted-foreground text-xs">
                        Initial Investment
                      </div>
                      <div className="text-foreground text-lg font-semibold">
                        $50,000
                      </div>
                    </div>
                    <div className="border-border/50 bg-background/30 rounded-lg border p-3">
                      <div className="text-muted-foreground text-xs">
                        Final Value
                      </div>
                      <div className="text-lg font-semibold text-emerald-600">
                        $113,500
                      </div>
                    </div>
                  </div>

                  {/* Chart Area */}
                  <div className="border-border/50 bg-background/30 rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-muted-foreground text-xs">
                        Growth Over Time
                      </span>
                      <span className="text-xs font-medium text-emerald-600">
                        ↗ +7.2% annually
                      </span>
                    </div>
                    <div className="flex h-16 items-end gap-1">
                      {[30, 35, 42, 48, 55, 62, 70, 78, 85, 92].map(
                        (height, i) => (
                          <div
                            key={i}
                            className="flex-1 rounded-sm bg-gradient-to-t from-emerald-500/40 to-emerald-500/20"
                            style={{ height: `${height}%` }}
                          />
                        ),
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="border-border bg-background absolute -top-4 right-0 rounded-lg border p-3 shadow-lg">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-3 w-3 text-emerald-500" />
                  <span className="text-xs font-medium">Growing</span>
                </div>
              </div>

              <div className="border-border bg-background absolute -bottom-4 -left-4 rounded-lg border p-3 shadow-lg">
                <div className="flex items-center gap-2">
                  <PieChart className="h-3 w-3 text-blue-500" />
                  <span className="text-xs font-medium">Diversified</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
