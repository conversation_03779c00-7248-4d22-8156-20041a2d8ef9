"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Calendar,
  Percent,
  Copy,
  Download,
  Heart,
  Share
} from 'lucide-react';
import { 
  BaseCalculationResult, 
  LumpSumResult, 
  DCAResult, 
  RetirementResult, 
  SavingsGoalResult,
  SupportedCurrency 
} from '@/lib/calculations/types';
import { formatCurrency, CurrencyUtils } from '@/lib/utils/currency';

interface ResultCardProps {
  result: BaseCalculationResult;
  title?: string;
  showActions?: boolean;
  onSave?: () => void;
  onShare?: () => void;
  onExport?: () => void;
  className?: string;
}

interface MetricItemProps {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className?: string }>;
  trend?: 'up' | 'down' | 'neutral';
  description?: string;
  highlight?: boolean;
}

function MetricItem({ 
  label, 
  value, 
  icon: Icon, 
  trend, 
  description, 
  highlight 
}: MetricItemProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn(
      "flex items-center justify-between p-3 rounded-lg border",
      highlight && "bg-primary/5 border-primary/20"
    )}>
      <div className="flex items-center gap-3">
        {Icon && <Icon className="h-5 w-5 text-muted-foreground" />}
        <div>
          <p className="text-sm font-medium">{label}</p>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <span className={cn(
          "text-lg font-bold",
          highlight && "text-primary"
        )}>
          {value}
        </span>
        {getTrendIcon()}
      </div>
    </div>
  );
}

export function ResultCard({ 
  result, 
  title, 
  showActions = true, 
  onSave, 
  onShare, 
  onExport,
  className 
}: ResultCardProps) {
  const currency = result.currency as SupportedCurrency;
  
  // 计算收益率
  const gainPercentage = result.totalContributions > 0 
    ? (result.totalGains / result.totalContributions) * 100 
    : 0;

  // 根据计算类型获取特定指标
  const getSpecificMetrics = () => {
    switch (result.calculationType) {
      case 'lump_sum':
        const lumpSumResult = result as LumpSumResult;
        return [
          {
            label: "Initial Investment",
            value: formatCurrency(lumpSumResult.principal, currency),
            icon: DollarSign,
            description: "Your starting amount"
          }
        ];

      case 'dca':
        const dcaResult = result as DCAResult;
        return [
          {
            label: "Average Cost Basis",
            value: formatCurrency(dcaResult.averageCostBasis, currency),
            icon: Target,
            description: "Average monthly contribution"
          }
        ];

      case 'retirement':
        const retirementResult = result as RetirementResult;
        return [
          {
            label: "Recommended Monthly",
            value: formatCurrency(retirementResult.recommendedMonthlyContribution, currency),
            icon: Target,
            description: "To reach your goal"
          },
          {
            label: "Inflation Adjusted Goal",
            value: formatCurrency(retirementResult.inflationAdjustedGoal, currency),
            icon: TrendingUp,
            description: "Future value of your goal"
          }
        ];

      case 'savings_goal':
        const savingsResult = result as SavingsGoalResult;
        return [
          {
            label: "Required Monthly",
            value: formatCurrency(savingsResult.requiredMonthlyContribution, currency),
            icon: Target,
            description: "To reach your target"
          },
          {
            label: "Time to Goal",
            value: savingsResult.timeToGoal.toFixed(1) + " years",
            icon: Calendar,
            description: "Estimated timeframe"
          }
        ];

      default:
        return [];
    }
  };

  const specificMetrics = getSpecificMetrics();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">
            {title || "Investment Results"}
          </CardTitle>
          <Badge variant="secondary" className="text-xs">
            {result.calculationType.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 主要结果 */}
        <div className="space-y-3">
          <MetricItem
            label="Final Amount"
            value={formatCurrency(result.finalAmount, currency)}
            icon={DollarSign}
            trend="up"
            highlight
            description="Total value at the end"
          />
          
          <MetricItem
            label="Total Contributions"
            value={formatCurrency(result.totalContributions, currency)}
            icon={Target}
            description="Amount you invested"
          />
          
          <MetricItem
            label="Total Gains"
            value={formatCurrency(result.totalGains, currency)}
            icon={TrendingUp}
            trend={result.totalGains >= 0 ? 'up' : 'down'}
            description="Your investment growth"
          />
        </div>

        <Separator />

        {/* 收益率指标 */}
        <div className="space-y-3">
          <MetricItem
            label="Total Return"
            value={CurrencyUtils.formatPercentage(gainPercentage)}
            icon={Percent}
            trend={gainPercentage >= 0 ? 'up' : 'down'}
            description="Overall gain percentage"
          />
          
          <MetricItem
            label="Effective Annual Rate"
            value={CurrencyUtils.formatPercentage(result.effectiveRate * 100)}
            icon={Percent}
            description="Compound annual growth rate"
          />
        </div>

        {/* 特定计算类型的指标 */}
        {specificMetrics.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              {specificMetrics.map((metric, index) => (
                <MetricItem key={index} {...metric} />
              ))}
            </div>
          </>
        )}

        {/* 操作按钮 */}
        {showActions && (
          <>
            <Separator />
            <div className="flex flex-wrap gap-2">
              {onSave && (
                <Button variant="outline" size="sm" onClick={onSave}>
                  <Heart className="h-4 w-4 mr-2" />
                  Save
                </Button>
              )}
              
              {onShare && (
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share className="h-4 w-4 mr-2" />
                  Share
                </Button>
              )}
              
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  const text = `Final Amount: ${formatCurrency(result.finalAmount, currency)}\nTotal Gains: ${formatCurrency(result.totalGains, currency)}\nTotal Return: ${CurrencyUtils.formatPercentage(gainPercentage)}`;
                  navigator.clipboard.writeText(text);
                }}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

// 简化版结果卡片（用于主页预览）
export function ResultCardSimple({ 
  result, 
  className 
}: { 
  result: BaseCalculationResult; 
  className?: string; 
}) {
  const currency = result.currency as SupportedCurrency;
  const gainPercentage = result.totalContributions > 0 
    ? (result.totalGains / result.totalContributions) * 100 
    : 0;

  return (
    <div className={cn("space-y-3 p-4 rounded-lg border bg-card", className)}>
      <div className="text-center">
        <p className="text-sm text-muted-foreground">Final Amount</p>
        <p className="text-2xl font-bold text-primary">
          {formatCurrency(result.finalAmount, currency, { compact: true })}
        </p>
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-center">
        <div>
          <p className="text-xs text-muted-foreground">Total Gains</p>
          <p className="text-sm font-semibold text-green-600">
            {formatCurrency(result.totalGains, currency, { compact: true })}
          </p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground">Return</p>
          <p className="text-sm font-semibold">
            {CurrencyUtils.formatPercentage(gainPercentage)}
          </p>
        </div>
      </div>
    </div>
  );
}
