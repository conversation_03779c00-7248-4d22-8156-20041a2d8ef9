import {
  LumpSumParams,
  DCAParams,
  RetirementParams,
  SavingsGoalParams,
  LumpSumResult,
  DCAResult,
  RetirementResult,
  SavingsGoalResult,
  YearlyBreakdown,
  MonthlyBreakdown,
  BaseCalculationResult,
  CalculationParams,
} from "./types";

/**
 * 投资计算引擎
 * 实现四种核心计算模式的算法
 */
export class InvestmentCalculationEngine {
  /**
   * 一次性投资复利计算
   * 使用复利公式: A = P(1 + r/n)^(nt)
   */
  static calculateLumpSum(params: LumpSumParams): LumpSumResult {
    const { principal, annualRate, years, compoundingFrequency = 12 } = params;

    const rate = annualRate / 100;
    const n = compoundingFrequency;
    const t = years;

    // 复利公式计算最终金额
    const finalAmount = principal * Math.pow(1 + rate / n, n * t);
    const totalGains = finalAmount - principal;

    // 计算有效年化收益率
    const effectiveRate = Math.pow(1 + rate / n, n) - 1;

    // 生成年度分解数据
    const yearlyBreakdown = this.generateLumpSumYearlyBreakdown(params);

    return {
      finalAmount,
      totalContributions: principal,
      totalGains,
      effectiveRate,
      calculationType: "lump_sum",
      currency: params.currency,
      principal,
      yearlyBreakdown,
    };
  }

  /**
   * 定投(DCA)计算
   * 计算定期投资的复合增长
   */
  static calculateDCA(params: DCAParams): DCAResult {
    const {
      initialInvestment = 0,
      monthlyContribution,
      annualRate,
      years,
      contributionIncrease = 0,
    } = params;

    const monthlyRate = annualRate / 100 / 12;
    const totalMonths = years * 12;

    let balance = initialInvestment;
    let totalContributions = initialInvestment;
    let currentMonthlyContribution = monthlyContribution;

    const monthlyBreakdown: MonthlyBreakdown[] = [];

    // 逐月计算
    for (let month = 1; month <= totalMonths; month++) {
      // 年度贡献增长（每12个月调整一次）
      if (month > 1 && (month - 1) % 12 === 0 && contributionIncrease > 0) {
        currentMonthlyContribution *= 1 + contributionIncrease / 100;
      }

      // 添加月度贡献
      balance += currentMonthlyContribution;
      totalContributions += currentMonthlyContribution;

      // 计算月度收益
      balance *= 1 + monthlyRate;

      const year = Math.ceil(month / 12);
      monthlyBreakdown.push({
        month,
        year,
        contribution: currentMonthlyContribution,
        balance,
        cumulativeContributions: totalContributions,
        cumulativeGains: balance - totalContributions,
      });
    }

    const finalAmount = balance;
    const totalGains = finalAmount - totalContributions;
    const averageCostBasis = totalContributions / totalMonths;

    return {
      finalAmount,
      totalContributions,
      totalGains,
      effectiveRate: annualRate / 100,
      calculationType: "dca",
      currency: params.currency,
      monthlyBreakdown,
      averageCostBasis,
    };
  }

  /**
   * 退休规划计算
   * 计算退休储蓄是否足够达到目标
   */
  static calculateRetirement(params: RetirementParams): RetirementResult {
    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyContribution,
      expectedReturn,
      inflationRate = 2.5,
      retirementGoal,
    } = params;

    const yearsToRetirement = retirementAge - currentAge;
    const monthlyRate = expectedReturn / 100 / 12;
    const totalMonths = yearsToRetirement * 12;

    // 计算当前储蓄的未来价值
    const currentSavingsFutureValue =
      currentSavings * Math.pow(1 + expectedReturn / 100, yearsToRetirement);

    // 计算定投的未来价值（年金公式）
    const monthlyContributionFutureValue =
      monthlyContribution *
      ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate);

    const projectedRetirementSavings =
      currentSavingsFutureValue + monthlyContributionFutureValue;

    // 通胀调整后的退休目标
    const inflationAdjustedGoal =
      retirementGoal * Math.pow(1 + inflationRate / 100, yearsToRetirement);

    const shortfall = Math.max(
      0,
      inflationAdjustedGoal - projectedRetirementSavings,
    );

    // 计算达到目标所需的月度贡献
    const requiredTotalFutureValue =
      inflationAdjustedGoal - currentSavingsFutureValue;
    const requiredMonthlyContribution =
      requiredTotalFutureValue > 0
        ? requiredTotalFutureValue /
          ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate)
        : 0;

    return {
      finalAmount: projectedRetirementSavings,
      totalContributions: currentSavings + monthlyContribution * totalMonths,
      totalGains:
        projectedRetirementSavings -
        (currentSavings + monthlyContribution * totalMonths),
      effectiveRate: expectedReturn / 100,
      calculationType: "retirement",
      currency: params.currency,
      shortfall,
      recommendedMonthlyContribution: Math.max(0, requiredMonthlyContribution),
      inflationAdjustedGoal,
    };
  }

  /**
   * 储蓄目标计算
   * 计算达到特定金额所需的投资计划
   */
  static calculateSavingsGoal(params: SavingsGoalParams): SavingsGoalResult {
    const {
      targetAmount,
      timeframe,
      initialAmount = 0,
      expectedReturn,
      contributionFrequency,
    } = params;

    const annualRate = expectedReturn / 100;
    let periodsPerYear: number;

    switch (contributionFrequency) {
      case "monthly":
        periodsPerYear = 12;
        break;
      case "quarterly":
        periodsPerYear = 4;
        break;
      case "annually":
        periodsPerYear = 1;
        break;
    }

    const periodRate = annualRate / periodsPerYear;
    const totalPeriods = timeframe * periodsPerYear;

    // 计算初始金额的未来价值
    const initialFutureValue =
      initialAmount * Math.pow(1 + annualRate, timeframe);

    // 计算需要通过定期贡献达到的金额
    const requiredFromContributions = targetAmount - initialFutureValue;

    // 计算所需的定期贡献（年金公式）
    const requiredPeriodicContribution =
      requiredFromContributions > 0
        ? requiredFromContributions /
          ((Math.pow(1 + periodRate, totalPeriods) - 1) / periodRate)
        : 0;

    // 转换为月度贡献（用于显示）
    const requiredMonthlyContribution =
      contributionFrequency === "monthly"
        ? requiredPeriodicContribution
        : (requiredPeriodicContribution * periodsPerYear) / 12;

    const totalContributions =
      initialAmount + requiredPeriodicContribution * totalPeriods;

    return {
      finalAmount: targetAmount,
      totalContributions,
      totalGains: targetAmount - totalContributions,
      effectiveRate: annualRate,
      calculationType: "savings_goal",
      currency: params.currency,
      requiredMonthlyContribution,
      timeToGoal: timeframe,
      projectedShortfall: Math.max(0, requiredFromContributions),
    };
  }

  /**
   * 生成一次性投资的年度分解数据
   */
  private static generateLumpSumYearlyBreakdown(
    params: LumpSumParams,
  ): YearlyBreakdown[] {
    const { principal, annualRate, years, compoundingFrequency = 12 } = params;

    const rate = annualRate / 100;
    const n = compoundingFrequency;
    const effectiveAnnualRate = Math.pow(1 + rate / n, n) - 1;

    const yearlyBreakdown: YearlyBreakdown[] = [];
    let currentBalance = principal;

    for (let year = 1; year <= years; year++) {
      const startingBalance = currentBalance;
      const yearlyGrowth = startingBalance * effectiveAnnualRate;
      currentBalance = startingBalance + yearlyGrowth;

      yearlyBreakdown.push({
        year,
        startingBalance,
        contribution: year === 1 ? principal : 0,
        interest: yearlyGrowth,
        endingBalance: currentBalance,
      });
    }

    return yearlyBreakdown;
  }

  /**
   * 通用计算方法
   * 根据类型调用相应的计算方法
   */
  static calculate(
    type: string,
    params: CalculationParams,
  ): BaseCalculationResult {
    switch (type) {
      case "lump_sum":
        return this.calculateLumpSum(params as LumpSumParams);
      case "dca":
        return this.calculateDCA(params as DCAParams);
      case "retirement":
        return this.calculateRetirement(params as RetirementParams);
      case "savings_goal":
        return this.calculateSavingsGoal(params as SavingsGoalParams);
      default:
        throw new Error(`Unsupported calculation type: ${type}`);
    }
  }
}
