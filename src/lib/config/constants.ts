// Brand Configuration
export const APP_NAME =
  process.env.NODE_ENV === "development"
    ? "DEV - SaaS Starter"
    : "SaaS Starter";
export const APP_DESCRIPTION =
  "Complete UllrAI SaaS starter with authentication, payments, database, and deployment.";
export const COMPANY_NAME = "UllrAI Lab";
export const COMPANY_TAGLINE = "by UllrAI, for developers";

export const TRIAL_DAYS = 7;

// https://www.dicebear.com/playground/
// DEFAULT: initials
export const AVATAR_STYLE = "adventurer-neutral";

// Contact Information
export const CONTACT_EMAIL = "<EMAIL>";
export const LEGAL_EMAIL = "<EMAIL>";
export const PRIVACY_EMAIL = "<EMAIL>";
export const RESEND_EMAIL_FROM = "<EMAIL>";

// External Links
export const GITHUB_URL = "https://github.com/ullrai/saas-starter";
export const VERCEL_DEPLOY_URL =
  "https://vercel.com/new/clone?repository-url=https://github.com/ullrai/saas-starter";

export const PAYMENT_PROVIDER = "creem" as const;
// SEO
export const OGIMAGE = "https://starter.ullrai.com/og.png";
export const TWITTERACCOUNT = "@hulebaji";
